from Cython.TestUtils import CythonTest

class TestCodeWriter(CythonTest):
    # CythonTest uses the CodeWriter heavily, so do some checking by
    # roundtripping Cython code through the test framework.

    # Note that this test is dependent upon the normal Cython parser
    # to generate the input trees to the CodeWriter. This save *a lot*
    # of time; better to spend that time writing other tests than perfecting
    # this one...

    # Whitespace is very significant in this process:
    #  - always newline on new block (!)
    #  - indent 4 spaces
    #  - 1 space around every operator

    def t(self, codestr):
        self.assertCode(codestr, self.fragment(codestr).root)

    def test_print(self):
        self.t("""
                    print(x + y ** 2)
                    print(x, y, z)
                    print(x + y, x + y * z, x * (y + z))
               """)

    def test_if(self):
        self.t("if x:\n    pass")

    def test_ifelifelse(self):
        self.t("""
                    if x:
                        pass
                    elif y:
                        pass
                    elif z + 34 ** 34 - 2:
                        pass
                    else:
                        pass
                """)

    def test_def(self):
        self.t("""
                    def f(x, y, z):
                        pass
                    def f(x = 34, y = 54, z):
                        pass
               """)

    def test_cdef(self):
        self.t("""
                    cdef f(x, y, z):
                        pass
                    cdef public void (x = 34, y = 54, z):
                        pass
                    cdef f(int *x, void *y, Value *z):
                        pass
                    cdef f(int **x, void **y, Value **z):
                        pass
                    cdef inline f(int &x, Value &z):
                        pass
               """)

    def test_longness_and_signedness(self):
        self.t("def f(unsigned long long long long long int y):\n    pass")

    def test_signed_short(self):
        self.t("def f(signed short int y):\n    pass")

    def test_typed_args(self):
        self.t("def f(int x, unsigned long int y):\n    pass")

    def test_cdef_var(self):
        self.t("""
                    cdef int hello
                    cdef int hello = 4, x = 3, y, z
                """)

    def test_for_loop(self):
        self.t("""
                    for x, y, z in f(g(h(34) * 2) + 23):
                        print(x, y, z)
                    else:
                        print(43)
                """)
        self.t("""
                    for abc in (1, 2, 3):
                        print(x, y, z)
                    else:
                        print(43)
                """)

    def test_while_loop(self):
        self.t("""
                    while True:
                        while True:
                            while True:
                                continue
                """)

    def test_inplace_assignment(self):
        self.t("x += 43")

    def test_cascaded_assignment(self):
        self.t("x = y = z = abc = 43")

    def test_attribute(self):
        self.t("a.x")

    def test_return_none(self):
        self.t("""
                    def f(x, y, z):
                        return
                    cdef f(x, y, z):
                        return
                    def f(x, y, z):
                        return None
                    cdef f(x, y, z):
                        return None
                    def f(x, y, z):
                        return 1234
                    cdef f(x, y, z):
                        return 1234
               """)

if __name__ == "__main__":
    import unittest
    unittest.main()
