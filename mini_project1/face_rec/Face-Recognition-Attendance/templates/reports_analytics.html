<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports & Analytics - Face Recognition Attendance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/modern-ui.css') }}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: #1e293b;
        }
        .reports-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .reports-card {
            background: #fff;
            border-radius: 1.5rem;
            box-shadow: 0 10px 32px rgba(37,99,235,0.10);
            border: 1px solid #e5e7eb;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .reports-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2563eb;
            margin-bottom: 2rem;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 1rem;
            text-align: center;
            box-shadow: 0 8px 25px rgba(37,99,235,0.3);
        }
        .stat-card.success {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        }
        .stat-card.danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .chart-container {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .filter-section {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
        }
        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37,99,235,0.3);
        }
        .table-container {
            background: white;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .table {
            margin-bottom: 0;
        }
        .table thead th {
            background: #f8fafc;
            border: none;
            font-weight: 600;
            color: #475569;
            padding: 1rem;
        }
        .table tbody td {
            padding: 1rem;
            border-color: #e2e8f0;
        }
        .back-btn {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
            color: white;
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100,116,139,0.3);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="reports-container">
        <div class="reports-card">
            <h1 class="reports-title">
                <i class="fas fa-chart-bar me-3"></i>Reports & Analytics
            </h1>
            
            <!-- Statistics Overview -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ total_students }}</div>
                    <div class="stat-label">Total Students</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number">{{ today_present }}</div>
                    <div class="stat-label">Present Today</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number">{{ today_absent }}</div>
                    <div class="stat-label">Absent Today</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-number">{{ avg_attendance }}%</div>
                    <div class="stat-label">Average Attendance</div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-section">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="department" class="form-label">Department</label>
                        <select class="form-control" id="department" name="department">
                            <option value="">All Departments</option>
                            {% for dept in departments %}
                                <option value="{{ dept }}" {% if selected_dept == dept %}selected{% endif %}>{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>Apply Filters
                        </button>
                    </div>
                </form>
            </div>

            <!-- Charts Section -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="mb-3">Daily Attendance Trend</h5>
                        <canvas id="attendanceChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="mb-3">Department-wise Attendance</h5>
                        <canvas id="departmentChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Top Students Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Student ID</th>
                            <th>Name</th>
                            <th>Department</th>
                            <th>Total Days</th>
                            <th>Present Days</th>
                            <th>Attendance %</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in student_stats %}
                        <tr>
                            <td>{{ student.student_code }}</td>
                            <td>{{ student.name }}</td>
                            <td>{{ student.department }}</td>
                            <td>{{ student.total_days }}</td>
                            <td>{{ student.present_days }}</td>
                            <td>
                                <span class="badge {% if student.attendance_percentage >= 80 %}bg-success{% elif student.attendance_percentage >= 60 %}bg-warning{% else %}bg-danger{% endif %}">
                                    {{ "%.1f"|format(student.attendance_percentage) }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Export Options -->
            <div class="text-center mt-4">
                <a href="{{ url_for('export_detailed_report') }}?start_date={{ start_date }}&end_date={{ end_date }}&department={{ selected_dept }}" 
                   class="btn btn-success me-3">
                    <i class="fas fa-file-excel me-2"></i>Export Detailed Report
                </a>
                <a href="{{ url_for('export_summary_report') }}?start_date={{ start_date }}&end_date={{ end_date }}&department={{ selected_dept }}" 
                   class="btn btn-info me-3">
                    <i class="fas fa-file-pdf me-2"></i>Export Summary Report
                </a>
                <a href="{{ url_for('admin_panel') }}" class="back-btn">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin Panel
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Daily Attendance Chart
        const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
        new Chart(attendanceCtx, {
            type: 'line',
            data: {
                labels: {{ daily_labels | safe }},
                datasets: [{
                    label: 'Daily Attendance',
                    data: {{ daily_data | safe }},
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Department Chart
        const departmentCtx = document.getElementById('departmentChart').getContext('2d');
        new Chart(departmentCtx, {
            type: 'doughnut',
            data: {
                labels: {{ dept_labels | safe }},
                datasets: [{
                    data: {{ dept_data | safe }},
                    backgroundColor: [
                        '#2563eb',
                        '#059669',
                        '#d97706',
                        '#dc2626',
                        '#7c3aed',
                        '#0891b2'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
