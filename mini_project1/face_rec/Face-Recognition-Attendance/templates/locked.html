<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>System Locked</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/lock-screen.css') }}" rel="stylesheet">
</head>
<body>
    <div class="lock-container animate-fade-in" data-aos="zoom-in">
        <div class="lock-icon animate-pulse" data-aos="fade-down" data-aos-delay="100">
            <i class="fas fa-lock"></i>
        </div>

        <h1 class="lock-title animate-slide-up" data-aos="fade-up" data-aos-delay="200">System Locked</h1>

        <p class="lock-message animate-slide-up" data-aos="fade-up" data-aos-delay="300">
            The system is password protected. Please enter the admin password to unlock.
        </p>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                <div class="error-message animate-shake" data-aos="fade-in" data-aos-delay="400">
                    <div>
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span>{{ message }}</span>
                    </div>
                    {% if lockout_remaining > 0 %}
                    <div id="countdown-timer" class="countdown-timer" data-seconds="{{ lockout_remaining }}">
                        <span id="countdown-value">{{ lockout_remaining }}</span> seconds remaining
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" class="lock-form w-100" action="{{ url_for('unlock') }}" data-aos="fade-up" data-aos-delay="500">
            <div class="input-container">
                <input type="password" name="password" id="password" placeholder="Enter password" required autofocus>
                <i class="fas fa-lock"></i>
                <span class="toggle-password" id="togglePassword" tabindex="0" aria-label="Show password">
                    <i class="fas fa-eye"></i>
                </span>
            </div>

            <button type="submit" class="btn btn-danger w-100 py-3 mb-3">
                <i class="fas fa-unlock me-2"></i> Unlock System
            </button>

            <a href="{{ url_for('reset_password') }}" class="btn btn-outline-primary w-100 py-3">
                <i class="fas fa-key me-2"></i> Forgot Password?
            </a>
        </form>

        <!-- "Return to Home" button removed as requested -->
    </div>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'cubic-bezier(0.25, 0.1, 0.25, 1.0)',
            once: true,
            offset: 50,
            delay: 100
        });

        // Password show/hide toggle
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const togglePassword = document.getElementById('togglePassword');
            const eyeIcon = togglePassword ? togglePassword.querySelector('i') : null;

            if (passwordInput && togglePassword && eyeIcon) {
                function toggle() {
                    const isShown = passwordInput.type === 'text';
                    passwordInput.type = isShown ? 'password' : 'text';
                    eyeIcon.classList.toggle('fa-eye', isShown);
                    eyeIcon.classList.toggle('fa-eye-slash', !isShown);
                    togglePassword.setAttribute('aria-label', isShown ? 'Show password' : 'Hide password');

                    // Add animation
                    togglePassword.classList.add('animate-pulse');
                    setTimeout(() => {
                        togglePassword.classList.remove('animate-pulse');
                    }, 300);
                }

                togglePassword.addEventListener('click', toggle);
                togglePassword.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        toggle();
                    }
                });
            }

            // Add smooth page transitions
            document.body.classList.add('animate-fade-in');

            // Focus on password field
            if (passwordInput) {
                setTimeout(() => {
                    passwordInput.focus();
                }, 800);
            }

            // Countdown timer
            const countdownTimer = document.getElementById('countdown-timer');
            if (countdownTimer) {
                const countdownValue = document.getElementById('countdown-value');
                let seconds = parseInt(countdownTimer.getAttribute('data-seconds'));

                // Disable the form if we're in lockout
                if (seconds > 0) {
                    const passwordInput = document.getElementById('password');
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (passwordInput) passwordInput.disabled = true;
                    if (submitButton) submitButton.disabled = true;
                }

                const countdownInterval = setInterval(() => {
                    seconds--;
                    if (countdownValue) countdownValue.textContent = seconds;

                    if (seconds <= 0) {
                        clearInterval(countdownInterval);
                        // Enable the form again
                        const passwordInput = document.getElementById('password');
                        const submitButton = document.querySelector('button[type="submit"]');
                        if (passwordInput) passwordInput.disabled = false;
                        if (submitButton) submitButton.disabled = false;

                        // Reload the page to clear the lockout message
                        window.location.reload();
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>