upstream flask_app {
    server web:5000;
}

upstream webrtc_server {
    server webrtc:8080;
}

upstream pgadmin_server {
    server pgadmin:5050;
}

server {
    listen 80;
    server_name _;

    # For local deployment, we'll use HTTP instead of forcing HTTPS
    # Remove the comment below to force HTTPS in production
    # return 301 https://$host$request_uri;

    # Proxy headers
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # WebSocket support
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";

    # Timeouts
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # Static files
    location /static/ {
        alias /app/static/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # Uploads
    location /uploads/ {
        alias /app/uploads/;
        internal;
    }

    # Main application
    location / {
        proxy_pass http://flask_app;
        proxy_redirect off;
        proxy_buffering off;
    }

    # WebRTC server
    location /webrtc/ {
        proxy_pass http://webrtc_server/;
        proxy_redirect off;
        proxy_buffering off;
    }

    # pgAdmin
    location /pgadmin/ {
        proxy_pass http://pgadmin_server/;
        proxy_set_header X-Script-Name /pgadmin;
        proxy_redirect off;
    }
}

# Uncomment this server block for HTTPS in production
# server {
#     listen 443 ssl;
#     server_name _;
#
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#
#     # Security headers
#     add_header X-Frame-Options "SAMEORIGIN" always;
#     add_header X-XSS-Protection "1; mode=block" always;
#     add_header X-Content-Type-Options "nosniff" always;
#     add_header Referrer-Policy "no-referrer-when-downgrade" always;
#     add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#
#     # Proxy headers
#     proxy_set_header Host $host;
#     proxy_set_header X-Real-IP $remote_addr;
#     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#     proxy_set_header X-Forwarded-Proto $scheme;
#
#     # WebSocket support
#     proxy_http_version 1.1;
#     proxy_set_header Upgrade $http_upgrade;
#     proxy_set_header Connection "upgrade";
#
#     # Timeouts
#     proxy_connect_timeout 60s;
#     proxy_send_timeout 60s;
#     proxy_read_timeout 60s;
#
#     # Static files
#     location /static/ {
#         alias /app/static/;
#         expires 30d;
#         add_header Cache-Control "public, no-transform";
#     }
#
#     # Uploads
#     location /uploads/ {
#         alias /app/uploads/;
#         internal;
#     }
#
#     # Main application
#     location / {
#         proxy_pass http://flask_app;
#         proxy_redirect off;
#         proxy_buffering off;
#     }
#
#     # WebRTC server
#     location /webrtc/ {
#         proxy_pass http://webrtc_server/;
#         proxy_redirect off;
#         proxy_buffering off;
#     }
#
#     # pgAdmin
#     location /pgadmin/ {
#         proxy_pass http://pgadmin_server/;
#         proxy_set_header X-Script-Name /pgadmin;
#         proxy_redirect off;
#     }
# }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}