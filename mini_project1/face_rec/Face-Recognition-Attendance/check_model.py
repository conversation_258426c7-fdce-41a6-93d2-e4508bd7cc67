#!/usr/bin/env python3
"""
Script to check which InsightFace model is being used in the application.
This will print out detailed information about the model pack and individual models.
"""

import os
import sys
import logging
import glob
import numpy as np
import cv2
from insightface.app import FaceAnalysis
from insightface.utils import DEFAULT_MP_NAME

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_insightface_model():
    """Check which InsightFace model is being used and print details."""
    try:
        # Initialize InsightFace with the same parameters as in the main app
        logger.info("Initializing InsightFace...")
        app = FaceAnalysis(providers=['CPUExecutionProvider'])
        
        # Print the default model pack name
        logger.info(f"Default model pack name: {DEFAULT_MP_NAME}")
        
        # Print the model directory
        model_dir = os.path.expanduser(f"~/.insightface/models/{DEFAULT_MP_NAME}")
        logger.info(f"Model directory: {model_dir}")
        
        # List all ONNX files in the model directory
        onnx_files = glob.glob(os.path.join(model_dir, "*.onnx"))
        logger.info(f"Found {len(onnx_files)} ONNX model files:")
        for onnx_file in onnx_files:
            logger.info(f"  - {os.path.basename(onnx_file)}")
        
        # Prepare the model
        logger.info("Preparing InsightFace models...")
        app.prepare(ctx_id=0, det_size=(640, 640))
        
        # Print information about each loaded model
        logger.info("Loaded models:")
        for taskname, model in app.models.items():
            logger.info(f"  - Task: {taskname}")
            logger.info(f"    File: {os.path.basename(model.model_file)}")
            logger.info(f"    Input shape: {model.input_shape}")
            logger.info(f"    Input mean: {model.input_mean}")
            logger.info(f"    Input std: {model.input_std}")
            
        # Print detection model details
        det_model = app.det_model
        logger.info(f"Detection model type: {type(det_model).__name__}")
        
        # Test with a simple image
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        cv2.rectangle(test_img, (30, 30), (70, 70), (255, 255, 255), -1)  # White square as a dummy face
        
        logger.info("Testing model with a dummy image...")
        try:
            faces = app.get(test_img)
            logger.info(f"Test result: detected {len(faces)} faces")
        except Exception as e:
            logger.warning(f"Test failed: {e}")
        
        return app
    except Exception as e:
        logger.error(f"Error checking InsightFace model: {e}")
        return None

if __name__ == "__main__":
    logger.info("Starting InsightFace model check...")
    app = check_insightface_model()
    logger.info("Model check completed.")
