/* Ultra-Fast Response Optimizations for Face Recognition System */

/* Global Performance Optimizations */
* {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform-style: preserve-3d;
}

/* Immediate button feedback */
.btn {
    transition: all 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    will-change: transform, opacity, box-shadow;
    transform: translateZ(0);
}

.btn:hover {
    transform: translateY(-2px) translateZ(0) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.btn:active {
    transform: scale(0.97) translateZ(0) !important;
    opacity: 0.9 !important;
    transition: all 0.05s ease !important;
}

/* Quick spinner for better UX */
.quick-spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimized form elements */
input, select, textarea {
    transition: border-color 0.1s ease !important;
}

/* Fast loading states */
.loading-fast {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.loading-fast::after {
    content: "⏳";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Optimize table rendering */
.modern-table {
    contain: layout style paint;
    transform: translateZ(0);
}

.modern-table tbody tr {
    will-change: background-color;
}

/* Fast flash messages */
.flash-message {
    animation: slideInFast 0.2s ease-out !important;
}

@keyframes slideInFast {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Optimize card animations */
.modern-card {
    animation: fadeInFast 0.3s ease-out !important;
}

@keyframes fadeInFast {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fast hover effects */
.modern-table tbody tr:hover {
    transition: all 0.1s ease !important;
}

/* Optimize search input */
.modern-search input {
    transition: border-color 0.1s ease !important;
}

/* Quick button states */
.btn-processing {
    opacity: 0.8;
    transform: scale(0.98);
    pointer-events: none;
}

/* Performance optimizations */
* {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* GPU acceleration for smooth animations */
.btn, .modern-table tbody tr, .flash-message {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* Reduce repaints */
.modern-table-container {
    contain: layout style paint;
}

/* Fast checkbox animations */
input[type="checkbox"] {
    transition: all 0.1s ease !important;
}

/* Optimize dropdown performance */
.dropdown-menu {
    will-change: opacity, transform;
    transition: all 0.15s ease !important;
}

/* Fast navigation */
.navbar-nav .nav-link {
    transition: color 0.1s ease !important;
}

/* Optimize modal performance */
.modal {
    will-change: opacity;
}

.modal-dialog {
    will-change: transform;
}

/* Fast form validation feedback */
.is-invalid {
    transition: border-color 0.1s ease !important;
}

.is-valid {
    transition: border-color 0.1s ease !important;
}

/* Optimize pagination */
.pagination .page-link {
    transition: all 0.1s ease !important;
}

/* Fast loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-overlay .spinner {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

/* Reduce layout thrashing */
.container, .row, .col {
    contain: layout;
}

/* Fast scroll performance */
.modern-table-container {
    scroll-behavior: auto;
    -webkit-overflow-scrolling: touch;
}

/* Optimize image loading */
img {
    will-change: opacity;
}

/* Fast transitions for all interactive elements */
a, button, input, select, textarea {
    transition-duration: 0.1s !important;
}

/* Prevent unnecessary reflows */
.fixed-layout {
    table-layout: fixed;
}

/* Fast focus states */
:focus {
    transition: box-shadow 0.1s ease !important;
}

/* Optimize for mobile performance */
@media (max-width: 768px) {
    .btn {
        transition-duration: 0.05s !important;
    }

    .modern-table tbody tr:hover {
        transform: none !important;
    }

    .modern-card {
        animation: none !important;
    }
}

/* Disable animations on slow devices */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Critical performance optimizations */
.performance-critical {
    contain: strict;
    will-change: auto;
}

/* Fast form submission feedback */
.form-submitting {
    opacity: 0.7;
    pointer-events: none;
}

.form-submitting .btn {
    position: relative;
}

.form-submitting .btn::after {
    content: "⏳";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}
