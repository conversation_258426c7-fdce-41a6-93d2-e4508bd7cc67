/* Lock Screen Styles for Face Recognition Attendance System
   Created by Augment Agent - Professional UI Upgrade
*/

:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);

    /* Secondary Colors */
    --success-color: #059669;
    --success-light: #10b981;
    --success-dark: #047857;
    --success-gradient: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);

    /* Alert Colors */
    --danger-color: #dc2626;
    --danger-light: #ef4444;
    --danger-dark: #b91c1c;
    --danger-gradient: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);

    /* Neutral Colors */
    --background-color: #f8fafc;
    --card-background: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Animation Timing */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: 'Inter', sans-serif;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(37, 99, 235, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(5, 150, 105, 0.1) 0%, transparent 40%);
    background-attachment: fixed;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    margin: 0;
    color: var(--text-primary);
}

.lock-container {
    max-width: 500px;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1.5rem;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    position: relative;
    padding: 3rem 2rem;
    text-align: center;
}

.lock-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--danger-gradient);
}

.lock-icon {
    width: 100px;
    height: 100px;
    background: var(--danger-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    box-shadow: 0 10px 25px -5px rgba(220, 38, 38, 0.5);
    animation: pulse 2s infinite;
}

.lock-icon i {
    font-size: 3rem;
    color: white;
}

.lock-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--danger-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.lock-message {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.lock-form {
    margin-bottom: 2rem;
}

.input-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.input-container i {
    position: absolute;
    left: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    transition: all var(--transition-normal);
}

.input-container input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    font-size: 1rem;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.8);
}

.input-container input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
    background: white;
}

.input-container input:focus + i {
    color: var(--primary-color);
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.toggle-password:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
}

.error-message {
    background-color: rgba(254, 226, 226, 0.9);
    color: var(--danger-color);
    padding: 1rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    border-left: 4px solid var(--danger-color);
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

.error-message > div:first-child {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
}

.countdown-timer {
    background-color: rgba(254, 202, 202, 0.5);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    margin-top: 0.5rem;
    width: 100%;
    text-align: center;
    animation: pulse 1.5s infinite;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    font-size: 1rem;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 0;
}

.btn:hover::before {
    transform: translateX(0);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid currentColor;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: rgba(37, 99, 235, 0.1);
}

.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-outline-danger:hover {
    background-color: rgba(220, 38, 38, 0.1);
}

.action-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.action-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.action-link:hover {
    color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(4px, 0, 0); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Responsive Design */
@media (max-width: 576px) {
    .lock-container {
        padding: 2rem 1.5rem;
    }

    .lock-icon {
        width: 80px;
        height: 80px;
    }

    .lock-icon i {
        font-size: 2.5rem;
    }

    .lock-title {
        font-size: 2rem;
    }

    .lock-message {
        font-size: 1rem;
    }
}
