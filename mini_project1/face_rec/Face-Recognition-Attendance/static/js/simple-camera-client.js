/**
 * Simple Camera Client for Face Recognition Attendance System
 * Works on all browsers including Chrome on Mac M1
 */

class SimpleCameraClient {
    constructor(options = {}) {
        // Configuration
        this.videoElement = options.videoElement;
        this.statusElement = options.statusElement;
        this.mode = options.mode || 'attendance'; // 'attendance' or 'registration'
        this.studentId = options.studentId;
        this.onStatusChange = options.onStatusChange || (() => {});
        this.onAttendanceMarked = options.onAttendanceMarked || (() => {});
        this.onRegistrationComplete = options.onRegistrationComplete || (() => {});
        this.onFaceDetected = options.onFaceDetected || (() => {});

        // State
        this.stream = null;
        this.status = 'disconnected';
        this.captureCount = 0;
        this.captureInterval = null;
        this.processingImage = false;

        // Watchdog timer to detect and fix stalled face detection
        this.lastSuccessfulCapture = Date.now();
        this.watchdogTimer = null;
        this.watchdogInterval = 10000; // 10 seconds

        // Canvas for capturing images
        this.canvas = document.createElement('canvas');
        this.canvas.width = 640;
        this.canvas.height = 480;
        this.ctx = this.canvas.getContext('2d');

        // Create overlay canvas for face detection visualization
        this.overlayCanvas = document.createElement('canvas');
        this.overlayCanvas.style.position = 'absolute';
        this.overlayCanvas.style.top = '0';
        this.overlayCanvas.style.left = '0';
        this.overlayCanvas.style.width = '100%';
        this.overlayCanvas.style.height = '100%';
        this.overlayCanvas.style.pointerEvents = 'none';
        this.overlayCtx = this.overlayCanvas.getContext('2d');

        // Bind methods
        this.start = this.start.bind(this);
        this.stop = this.stop.bind(this);
        this.captureAndSendImage = this.captureAndSendImage.bind(this);
        this.updateStatus = this.updateStatus.bind(this);
        this.drawFaceDetectionBox = this.drawFaceDetectionBox.bind(this);
        this.resizeOverlayCanvas = this.resizeOverlayCanvas.bind(this);
        this.clearFaceBoxesIfNeeded = this.clearFaceBoxesIfNeeded.bind(this);
        this.startWatchdogTimer = this.startWatchdogTimer.bind(this);
        this.stopWatchdogTimer = this.stopWatchdogTimer.bind(this);
        this.restartCaptureProcess = this.restartCaptureProcess.bind(this);

        // Start the periodic check to clear face boxes
        setTimeout(this.clearFaceBoxesIfNeeded, 1000);
    }

    /**
     * Start the camera client
     */
    async start() {
        try {
            this.updateStatus('starting');

            // Dispatch appropriate start event
            if (this.mode === 'registration') {
                document.dispatchEvent(new CustomEvent('registration:start', {
                    detail: { processId: 'registration-' + this.studentId }
                }));
            } else if (this.mode === 'attendance') {
                document.dispatchEvent(new CustomEvent('attendance:start', {
                    detail: { processId: 'attendance-' + Date.now() }
                }));
            }

            // Get camera stream
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                }
            });

            // Set video source
            this.videoElement.srcObject = this.stream;
            await this.videoElement.play();

            // Start capturing
            this.isCapturing = true;
            this.captureInterval = setInterval(() => this.captureAndSendImage(), 100);

            // Start watchdog timer
            this.lastSuccessfulCapture = Date.now();
            this.startWatchdogTimer();

            this.updateStatus('connected', 'Connected to camera');
        } catch (error) {
            console.error('Error starting camera:', error);
            this.updateStatus('error', 'Failed to start camera: ' + error.message);

            // Dispatch appropriate stop event on error
            if (this.mode === 'registration') {
                document.dispatchEvent(new CustomEvent('registration:complete', {
                    detail: { processId: 'registration-' + this.studentId }
                }));
            } else if (this.mode === 'attendance') {
                document.dispatchEvent(new CustomEvent('attendance:stop', {
                    detail: { processId: 'attendance-' + Date.now() }
                }));
            }

            throw error;
        }
    }

    /**
     * Resize overlay canvas to match video element dimensions
     */
    resizeOverlayCanvas() {
        if (!this.videoElement || !this.overlayCanvas) return;

        // Get the actual displayed dimensions of the video element
        const videoRect = this.videoElement.getBoundingClientRect();

        // Set the overlay canvas to exactly match the video element's display size
        this.overlayCanvas.style.position = 'absolute';
        this.overlayCanvas.style.top = '0';
        this.overlayCanvas.style.left = '0';
        this.overlayCanvas.style.width = `${videoRect.width}px`;
        this.overlayCanvas.style.height = `${videoRect.height}px`;

        // Set the internal canvas dimensions to match the video's intrinsic dimensions
        // This ensures the face detection boxes are properly scaled
        this.overlayCanvas.width = this.canvas.width;
        this.overlayCanvas.height = this.canvas.height;
    }

    /**
     * Stop the camera client
     */
    stop() {
        console.log('Stopping camera client');

        // Set a flag to prevent immediate restart attempts
        this._stopping = true;

        // Stop capturing
        this.isCapturing = false;

        // Clear capture interval
        if (this.captureInterval) {
            clearInterval(this.captureInterval);
            this.captureInterval = null;
            console.log('Cleared capture interval');
        }

        // Clear watchdog timer
        this.stopWatchdogTimer();

        // Stop all tracks with a small delay to ensure proper cleanup
        if (this.stream) {
            // Create a local reference to the stream
            const streamToStop = this.stream;
            this.stream = null;

            // Use setTimeout to delay track stopping slightly
            setTimeout(() => {
                try {
                    streamToStop.getTracks().forEach(track => {
                        try {
                            track.stop();
                            console.log(`Stopped track: ${track.kind}`);
                        } catch (trackErr) {
                            console.warn(`Error stopping track: ${trackErr.message}`);
                        }
                    });
                } catch (streamErr) {
                    console.warn(`Error stopping stream: ${streamErr.message}`);
                }
            }, 100);
        }

        // Clear video element
        if (this.videoElement) {
            try {
                this.videoElement.srcObject = null;
                this.videoElement.pause();
                console.log('Cleared video element');
            } catch (videoErr) {
                console.warn(`Error clearing video element: ${videoErr.message}`);
            }
        }

        // Dispatch appropriate stop event
        if (this.mode === 'registration') {
            document.dispatchEvent(new CustomEvent('registration:complete', {
                detail: { processId: 'registration-' + this.studentId }
            }));
        } else if (this.mode === 'attendance') {
            document.dispatchEvent(new CustomEvent('attendance:stop', {
                detail: { processId: 'attendance-' + Date.now() }
            }));
        }

        this.updateStatus('disconnected', 'Disconnected');
    }

    // Track when the last face was detected
    lastFaceDetectionTime = 0;

    // Function to clear face boxes if no faces detected for a while
    clearFaceBoxesIfNeeded() {
        // If it's been more than 1 second since we last detected a face, clear the canvas
        const now = Date.now();
        if (now - this.lastFaceDetectionTime > 1000) {
            if (this.overlayCtx && this.overlayCanvas) {
                this.overlayCtx.clearRect(0, 0, this.overlayCanvas.width, this.overlayCanvas.height);
            }
        }

        // Check again in 500ms
        setTimeout(() => this.clearFaceBoxesIfNeeded(), 500);
    }

    /**
     * Draw face detection box on overlay canvas
     */
    drawFaceDetectionBox(x, y, width, height, clearCanvas = false) {
        if (!this.overlayCanvas || !this.overlayCtx) return;

        // Ensure the overlay canvas is properly sized
        this.resizeOverlayCanvas();

        // Only clear the canvas if explicitly requested
        // This allows drawing multiple face boxes
        if (clearCanvas) {
            this.overlayCtx.clearRect(0, 0, this.overlayCanvas.width, this.overlayCanvas.height);
        }

        // Draw face detection box with green color - only outline, no fill
        this.overlayCtx.strokeStyle = '#00FF00'; // Bright green
        this.overlayCtx.lineWidth = 1.5; // Even thinner line for subtlety
        this.overlayCtx.strokeRect(x, y, width, height);

        // Add a smaller "Face Detected" label
        this.overlayCtx.font = '8px Arial';
        this.overlayCtx.fillStyle = '#00FF00'; // Bright green
        this.overlayCtx.fillText('Face Detected', x, Math.max(y - 2, 10));

        // Update the last face detection time
        this.lastFaceDetectionTime = Date.now();
    }

    /**
     * Capture and send image to server
     */
    async captureAndSendImage() {
        // Skip if already processing or video not ready
        if (this.processingImage || !this.videoElement || !this.stream ||
            !this.videoElement.videoWidth || this.videoElement.videoWidth === 0) {
            return;
        }

        try {
            this.processingImage = true;

            // Draw video frame to canvas
            this.ctx.drawImage(
                this.videoElement,
                0, 0,
                this.canvas.width,
                this.canvas.height
            );

            // Convert canvas to blob
            const blob = await new Promise(resolve => {
                this.canvas.toBlob(resolve, 'image/jpeg', 0.8);
            });

            // Create form data
            const formData = new FormData();
            formData.append('image', blob, 'capture.jpg');
            formData.append('mode', this.mode);
            formData.append('student_id', this.studentId || '');

            // Send to server
            const response = await fetch('/process_image', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}`);
            }

            const result = await response.json();

            // Update last successful capture time
            this.lastSuccessfulCapture = Date.now();

            // Handle different response types
            if (result.faces_detected) {
                // Face detected
                this.onFaceDetected({
                    type: 'face_detected',
                    count: result.faces_detected,
                    capture_count: result.capture_count || 0
                });

                // Handle face detection results
                if (result.faces && Array.isArray(result.faces) && result.faces.length > 0) {
                    // Pass the faces array to the onFaceDetected callback
                    this.onFaceDetected({
                        type: 'faces_detected',
                        faces: result.faces,
                        count: result.faces.length
                    });

                    // No need to draw here, the callback will handle it
                } else if (result.face_rect) {
                    // Fallback for single face
                    const { x, y, width, height } = result.face_rect;

                    // Pass the single face to the onFaceDetected callback
                    this.onFaceDetected({
                        type: 'face_detected',
                        face_rect: result.face_rect
                    });
                }

                // Update capture count for registration
                if (this.mode === 'registration' && result.capture_count !== undefined) {
                    this.captureCount = result.capture_count;

                    // Check if registration is complete
                    if (result.registration_complete) {
                        this.onRegistrationComplete({ success: true });
                    }
                }
            } else {
                // No faces detected, clear any face detection box
                if (this.overlayCtx) {
                    this.overlayCtx.clearRect(0, 0, this.overlayCanvas.width, this.overlayCanvas.height);
                }
            }

            if (result.attendance_marked) {
                // Attendance marked
                this.onAttendanceMarked({
                    type: 'attendance_marked',
                    name: result.name,
                    student_id: result.student_id,
                    timestamp: result.timestamp
                });
            } else if (result.already_marked_today) {
                // Attendance already marked for today
                this.onAttendanceMarked({
                    type: 'already_marked_today',
                    name: result.name,
                    student_id: result.student_id,
                    timestamp: result.timestamp
                });
            } else if (result.unknown_face) {
                // Unknown face detected
                this.onAttendanceMarked({
                    type: 'unknown_face',
                    message: result.message || 'Unknown face - not registered in the system'
                });
            }

        } catch (error) {
            console.error('Error capturing/sending image:', error);
        } finally {
            this.processingImage = false;
        }
    }

    /**
     * Update status
     */
    updateStatus(status, message) {
        this.status = status;

        if (this.statusElement) {
            this.statusElement.textContent = message;
            this.statusElement.className = `status-${status}`;
        }

        this.onStatusChange(status, message);
    }

    /**
     * Start the watchdog timer to detect and fix stalled face detection
     */
    startWatchdogTimer() {
        // Clear any existing watchdog timer
        this.stopWatchdogTimer();

        // Start a new watchdog timer
        this.watchdogTimer = setInterval(() => {
            // Check if it's been too long since the last successful capture
            const now = Date.now();
            const timeSinceLastCapture = now - this.lastSuccessfulCapture;

            // If it's been more than the watchdog interval, restart the capture process
            if (timeSinceLastCapture > this.watchdogInterval && this.isCapturing && !this._stopping) {
                console.log(`Watchdog: Face detection appears stalled (${timeSinceLastCapture}ms). Restarting capture process...`);

                // Attempt to restart the capture process
                this.restartCaptureProcess();
            }
        }, 5000); // Check every 5 seconds

        console.log('Watchdog timer started');
    }

    /**
     * Stop the watchdog timer
     */
    stopWatchdogTimer() {
        if (this.watchdogTimer) {
            clearInterval(this.watchdogTimer);
            this.watchdogTimer = null;
            console.log('Watchdog timer stopped');
        }
    }

    /**
     * Restart the capture process if it appears to be stalled
     */
    async restartCaptureProcess() {
        // Only proceed if we're not already stopping or processing
        if (this._stopping || this.processingImage) {
            return;
        }

        console.log('Attempting to restart capture process...');

        try {
            // Clear existing capture interval
            if (this.captureInterval) {
                clearInterval(this.captureInterval);
                this.captureInterval = null;
            }

            // Reset processing flag
            this.processingImage = false;

            // Restart the capture interval
            this.isCapturing = true;
            this.captureInterval = setInterval(() => this.captureAndSendImage(), 100);

            // Reset the last successful capture time
            this.lastSuccessfulCapture = Date.now();

            console.log('Capture process restarted successfully');
        } catch (error) {
            console.error('Error restarting capture process:', error);
        }
    }
}

// Export the class
window.SimpleCameraClient = SimpleCameraClient;
