/**
 * Fallback Client for Face Recognition Attendance System
 * For browsers that don't support WebRTC properly (like Chrome on Mac M1)
 *
 * This client uses a simple AJAX-based approach instead of WebRTC
 * It captures images from the camera and sends them to the server for processing
 */

class FallbackClient {
    constructor(options = {}) {
        // Configuration
        this.videoElement = options.videoElement;
        this.statusElement = options.statusElement;
        this.mode = options.mode || 'attendance'; // 'attendance' or 'registration'
        this.studentId = options.studentId;
        this.onStatusChange = options.onStatusChange || (() => {});
        this.onAttendanceMarked = options.onAttendanceMarked || (() => {});
        this.onRegistrationComplete = options.onRegistrationComplete || (() => {});
        this.onFaceDetected = options.onFaceDetected || (() => {});

        // State
        this.stream = null;
        this.status = 'disconnected';
        this.captureCount = 0;
        this.captureInterval = null;
        this.processingImage = false;
        this.faceDetectionBox = null;
        this.lastFaceDetectionTime = 0;

        // Canvas for capturing images
        this.canvas = document.createElement('canvas');
        this.canvas.width = 640;
        this.canvas.height = 480;
        this.ctx = this.canvas.getContext('2d');

        // Create overlay canvas for face detection visualization
        this.overlayCanvas = document.createElement('canvas');
        this.overlayCanvas.style.position = 'absolute';
        this.overlayCanvas.style.top = '0';
        this.overlayCanvas.style.left = '0';
        this.overlayCanvas.style.width = '100%';
        this.overlayCanvas.style.height = '100%';
        this.overlayCanvas.style.pointerEvents = 'none';
        this.overlayCtx = this.overlayCanvas.getContext('2d');

        // Bind methods
        this.start = this.start.bind(this);
        this.stop = this.stop.bind(this);
        this.captureAndSendImage = this.captureAndSendImage.bind(this);
        this.updateStatus = this.updateStatus.bind(this);
        this.drawFaceDetectionBox = this.drawFaceDetectionBox.bind(this);
        this.resizeOverlayCanvas = this.resizeOverlayCanvas.bind(this);
    }

    /**
     * Start the fallback client
     */
    async start() {
        try {
            this.updateStatus('connecting', 'Connecting to camera...');

            // Make sure any previous connection is closed
            this.stop();

            // Wait a moment for resources to be released
            await new Promise(resolve => setTimeout(resolve, 500));

            // Get user media with basic constraints
            try {
                // First try with exact constraints for Chrome on Mac
                this.stream = await navigator.mediaDevices.getUserMedia({
                    audio: false,
                    video: {
                        width: { exact: 640 },
                        height: { exact: 480 },
                        frameRate: { ideal: 30 }
                    }
                });
            } catch (err) {
                console.warn('Failed with exact settings, trying ideal settings', err);
                try {
                    this.stream = await navigator.mediaDevices.getUserMedia({
                        audio: false,
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 }
                        }
                    });
                } catch (err2) {
                    console.warn('Failed with ideal settings, trying basic video', err2);
                    this.stream = await navigator.mediaDevices.getUserMedia({
                        audio: false,
                        video: true
                    });
                }
            }

            // Display stream
            if (this.videoElement) {
                this.videoElement.srcObject = this.stream;

                // Wait for video to be ready
                await new Promise(resolve => {
                    this.videoElement.onloadedmetadata = () => {
                        // Set canvas dimensions to match video
                        this.canvas.width = this.videoElement.videoWidth;
                        this.canvas.height = this.videoElement.videoHeight;

                        // Add overlay canvas to video container
                        if (this.videoElement.parentElement) {
                            this.videoElement.parentElement.style.position = 'relative';
                            this.overlayCanvas.width = this.videoElement.videoWidth;
                            this.overlayCanvas.height = this.videoElement.videoHeight;
                            this.videoElement.parentElement.appendChild(this.overlayCanvas);

                            // Set up resize handler for responsive design
                            window.addEventListener('resize', this.resizeOverlayCanvas);
                            this.resizeOverlayCanvas();
                        }

                        resolve();
                    };
                    // Fallback if onloadedmetadata doesn't fire
                    setTimeout(() => {
                        if (this.videoElement.videoWidth) {
                            this.canvas.width = this.videoElement.videoWidth;
                            this.canvas.height = this.videoElement.videoHeight;
                        }
                        resolve();
                    }, 1000);
                });
            }

            this.updateStatus('connected', 'Connected to camera');

            // Start capturing images at regular intervals - faster for better responsiveness
            this.captureInterval = setInterval(this.captureAndSendImage, 500);

        } catch (error) {
            console.error('Error starting fallback client:', error);
            this.updateStatus('error', `Error: ${error.message}`);
            this.stop();
        }
    }

    /**
     * Resize overlay canvas to match video element dimensions
     */
    resizeOverlayCanvas() {
        if (!this.videoElement || !this.overlayCanvas) return;

        const videoRect = this.videoElement.getBoundingClientRect();
        this.overlayCanvas.style.width = `${videoRect.width}px`;
        this.overlayCanvas.style.height = `${videoRect.height}px`;
    }

    /**
     * Stop the fallback client
     */
    stop() {
        // Clear capture interval
        if (this.captureInterval) {
            clearInterval(this.captureInterval);
            this.captureInterval = null;
        }

        // Stop all tracks
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        // Clear video element
        if (this.videoElement) {
            this.videoElement.srcObject = null;
        }

        // Remove overlay canvas
        if (this.overlayCanvas && this.overlayCanvas.parentElement) {
            this.overlayCanvas.parentElement.removeChild(this.overlayCanvas);
        }

        // Remove resize listener
        window.removeEventListener('resize', this.resizeOverlayCanvas);

        // Notify server to release resources
        fetch('/release_fallback', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                mode: this.mode,
                student_id: this.studentId
            })
        }).catch(err => console.warn('Error releasing resources:', err));

        this.updateStatus('disconnected', 'Disconnected');
    }

    /**
     * Draw face detection box on overlay canvas
     */
    drawFaceDetectionBox(x, y, width, height) {
        if (!this.overlayCanvas || !this.overlayCtx) return;

        // Clear previous drawings
        this.overlayCtx.clearRect(0, 0, this.overlayCanvas.width, this.overlayCanvas.height);

        // Draw face detection box
        this.overlayCtx.strokeStyle = 'rgba(0, 255, 0, 0.8)';
        this.overlayCtx.lineWidth = 3;
        this.overlayCtx.strokeRect(x, y, width, height);

        // Store face detection box for later use
        this.faceDetectionBox = { x, y, width, height };
        this.lastFaceDetectionTime = Date.now();

        // Clear box after 1 second
        setTimeout(() => {
            if (Date.now() - this.lastFaceDetectionTime >= 1000) {
                this.overlayCtx.clearRect(0, 0, this.overlayCanvas.width, this.overlayCanvas.height);
                this.faceDetectionBox = null;
            }
        }, 1000);
    }

    /**
     * Capture and send image to server
     */
    async captureAndSendImage() {
        // Skip if already processing or video not ready
        if (this.processingImage || !this.videoElement || !this.stream ||
            !this.videoElement.videoWidth || this.videoElement.videoWidth === 0) {
            return;
        }

        try {
            this.processingImage = true;

            // Draw video frame to canvas
            this.ctx.drawImage(
                this.videoElement,
                0, 0,
                this.canvas.width,
                this.canvas.height
            );

            // Convert canvas to blob
            const blob = await new Promise(resolve => {
                this.canvas.toBlob(resolve, 'image/jpeg', 0.8);
            });

            // Create form data
            const formData = new FormData();
            formData.append('image', blob, 'capture.jpg');
            formData.append('mode', this.mode);
            formData.append('student_id', this.studentId || '');

            // Send to server
            const response = await fetch('/process_image', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}`);
            }

            const result = await response.json();

            // Handle different response types
            if (result.faces_detected) {
                // Face detected
                this.onFaceDetected({
                    type: 'face_detected',
                    count: result.faces_detected,
                    capture_count: result.capture_count || 0
                });

                // Draw face detection box if coordinates are provided
                if (result.face_rect) {
                    const { x, y, width, height } = result.face_rect;
                    this.drawFaceDetectionBox(x, y, width, height);
                }

                // Update capture count for registration
                if (this.mode === 'registration' && result.capture_count !== undefined) {
                    this.captureCount = result.capture_count;

                    // Show visual feedback
                    const videoContainer = this.videoElement.parentElement;
                    if (videoContainer) {
                        let indicator = videoContainer.querySelector('.face-detection-indicator');
                        if (!indicator) {
                            indicator = document.createElement('div');
                            indicator.className = 'face-detection-indicator';
                            indicator.style.position = 'absolute';
                            indicator.style.top = '10px';
                            indicator.style.right = '10px';
                            indicator.style.backgroundColor = 'rgba(0, 255, 0, 0.7)';
                            indicator.style.color = 'white';
                            indicator.style.padding = '5px 10px';
                            indicator.style.borderRadius = '5px';
                            indicator.style.fontWeight = 'bold';
                            indicator.style.zIndex = '100';
                            videoContainer.appendChild(indicator);
                        }

                        indicator.textContent = `Faces: ${result.faces_detected} | Captures: ${result.capture_count}/5`;
                        indicator.style.display = 'block';

                        // Hide after 2 seconds
                        setTimeout(() => {
                            indicator.style.display = 'none';
                        }, 2000);
                    }

                    // Check if registration is complete
                    if (result.registration_complete) {
                        this.handleRegistrationComplete();
                    }
                }
            } else {
                // No faces detected, clear any face detection box
                if (this.overlayCtx) {
                    this.overlayCtx.clearRect(0, 0, this.overlayCanvas.width, this.overlayCanvas.height);
                }
            }

            if (result.attendance_marked) {
                // Attendance marked
                this.onAttendanceMarked({
                    type: 'attendance_marked',
                    name: result.name,
                    student_id: result.student_id,
                    timestamp: result.timestamp
                });

                // Show attendance marked overlay
                const videoContainer = this.videoElement.parentElement;
                if (videoContainer) {
                    let overlay = document.getElementById('attendanceOverlay');
                    let nameOverlay = document.getElementById('studentNameOverlay');

                    if (overlay && nameOverlay) {
                        nameOverlay.textContent = result.name || 'Student';
                        overlay.style.display = 'flex';

                        setTimeout(() => {
                            overlay.style.display = 'none';
                        }, 1500);
                    }
                }
            }

        } catch (error) {
            console.error('Error capturing/sending image:', error);
        } finally {
            this.processingImage = false;
        }
    }

    /**
     * Handle registration complete
     */
    handleRegistrationComplete() {
        // Prevent multiple redirects
        if (window.registrationRedirectInitiated) return;

        window.registrationRedirectInitiated = true;
        this.stop();

        this.onRegistrationComplete({ success: true });

        // Show success message
        const successMessage = document.createElement('div');
        successMessage.className = 'alert alert-success registration-complete-message';
        successMessage.style.position = 'fixed';
        successMessage.style.top = '50%';
        successMessage.style.left = '50%';
        successMessage.style.transform = 'translate(-50%, -50%)';
        successMessage.style.zIndex = '9999';
        successMessage.style.padding = '20px';
        successMessage.style.borderRadius = '10px';
        successMessage.style.backgroundColor = '#dff0d8';
        successMessage.style.color = '#3c763d';
        successMessage.style.fontSize = '18px';
        successMessage.style.textAlign = 'center';
        successMessage.innerHTML = '<strong>Success!</strong><br>Face registration complete.<br>Redirecting...';
        document.body.appendChild(successMessage);

        // Redirect after delay
        setTimeout(() => {
            window.location.href = '/register?success=true';
        }, 1500);
    }

    /**
     * Update status
     */
    updateStatus(status, message) {
        this.status = status;

        if (this.statusElement) {
            this.statusElement.textContent = message;
            this.statusElement.className = `status-${status}`;
        }

        this.onStatusChange(status, message);
    }
}

// Export the class
window.FallbackClient = FallbackClient;
