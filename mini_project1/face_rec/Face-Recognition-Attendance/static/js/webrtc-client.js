/**
 * WebRTC client for Face Recognition Attendance System
 */

class WebRTCClient {
    constructor(options = {}) {
        // Configuration
        this.webrtcUrl = options.webrtcUrl || 'http://localhost:8080';
        this.videoElement = options.videoElement;
        this.statusElement = options.statusElement;
        this.onStatusChange = options.onStatusChange || (() => {});
        this.onAttendanceMarked = options.onAttendanceMarked || (() => {});
        
        // State
        this.pc = null;
        this.localStream = null;
        this.status = 'disconnected';
        
        // Bind methods
        this.start = this.start.bind(this);
        this.stop = this.stop.bind(this);
        this.createPeerConnection = this.createPeerConnection.bind(this);
        this.negotiate = this.negotiate.bind(this);
        this.updateStatus = this.updateStatus.bind(this);
        
        // Initialize
        this.initialize();
    }
    
    /**
     * Initialize the WebRTC client
     */
    initialize() {
        // Check if WebRTC is supported
        if (!navigator.mediaDevices || !RTCPeerConnection) {
            this.updateStatus('error', 'WebRTC is not supported in this browser');
            return;
        }
        
        // Add event listeners
        window.addEventListener('beforeunload', this.stop);
    }
    
    /**
     * Start the WebRTC connection
     */
    async start() {
        try {
            this.updateStatus('connecting', 'Connecting to camera...');
            
            // Get user media
            this.localStream = await navigator.mediaDevices.getUserMedia({
                audio: false,
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    frameRate: { ideal: 30 }
                }
            });
            
            // Create peer connection
            this.createPeerConnection();
            
            // Add local stream to peer connection
            this.localStream.getTracks().forEach(track => {
                this.pc.addTrack(track, this.localStream);
            });
            
            // Start negotiation
            await this.negotiate();
            
            this.updateStatus('connected', 'Connected to WebRTC server');
        } catch (error) {
            console.error('Error starting WebRTC:', error);
            this.updateStatus('error', `Error: ${error.message}`);
            this.stop();
        }
    }
    
    /**
     * Stop the WebRTC connection
     */
    stop() {
        // Close peer connection
        if (this.pc) {
            this.pc.close();
            this.pc = null;
        }
        
        // Stop local stream
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }
        
        // Update status
        this.updateStatus('disconnected', 'Disconnected');
    }
    
    /**
     * Create a new RTCPeerConnection
     */
    createPeerConnection() {
        const config = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' }
            ]
        };
        
        this.pc = new RTCPeerConnection(config);
        
        // Handle ICE connection state change
        this.pc.addEventListener('iceconnectionstatechange', () => {
            console.log('ICE connection state:', this.pc.iceConnectionState);
            
            if (this.pc.iceConnectionState === 'failed' || 
                this.pc.iceConnectionState === 'closed') {
                this.stop();
            }
        });
        
        // Handle track from server
        this.pc.addEventListener('track', (event) => {
            if (this.videoElement && event.track.kind === 'video') {
                this.videoElement.srcObject = event.streams[0];
            }
        });
    }
    
    /**
     * Negotiate the WebRTC connection
     */
    async negotiate() {
        try {
            // Create offer
            const offer = await this.pc.createOffer();
            await this.pc.setLocalDescription(offer);
            
            // Wait for ICE gathering to complete
            await new Promise(resolve => {
                if (this.pc.iceGatheringState === 'complete') {
                    resolve();
                } else {
                    const checkState = () => {
                        if (this.pc.iceGatheringState === 'complete') {
                            this.pc.removeEventListener('icegatheringstatechange', checkState);
                            resolve();
                        }
                    };
                    this.pc.addEventListener('icegatheringstatechange', checkState);
                }
            });
            
            // Get the final offer
            const offer_final = this.pc.localDescription;
            
            // Send offer to server
            const response = await fetch(`${this.webrtcUrl}/offer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sdp: offer_final.sdp,
                    type: offer_final.type
                })
            });
            
            // Get answer from server
            const answer = await response.json();
            
            // Set remote description
            await this.pc.setRemoteDescription(answer);
        } catch (error) {
            console.error('Error during negotiation:', error);
            this.updateStatus('error', `Negotiation error: ${error.message}`);
            this.stop();
        }
    }
    
    /**
     * Update the status of the WebRTC connection
     * @param {string} status - The new status
     * @param {string} message - The status message
     */
    updateStatus(status, message) {
        this.status = status;
        
        // Update status element if provided
        if (this.statusElement) {
            this.statusElement.textContent = message;
            this.statusElement.className = `status-${status}`;
        }
        
        // Call status change callback
        this.onStatusChange(status, message);
    }
}

// Export the WebRTCClient class
window.WebRTCClient = WebRTCClient;
