/**
 * Simplified WebRTC Client for Face Recognition Attendance System
 * Optimized for Chrome on Mac M1
 */

class UnifiedWebRTCClient {
    constructor(options = {}) {
        // Configuration
        this.videoElement = options.videoElement;
        this.statusElement = options.statusElement;
        this.mode = options.mode || 'attendance'; // 'attendance' or 'registration'
        this.studentId = options.studentId;
        this.onStatusChange = options.onStatusChange || (() => {});
        this.onAttendanceMarked = options.onAttendanceMarked || (() => {});
        this.onRegistrationComplete = options.onRegistrationComplete || (() => {});
        this.onFaceDetected = options.onFaceDetected || (() => {});

        // State
        this.pc = null;
        this.localStream = null;
        this.status = 'disconnected';
        this.captureCount = 0;
        
        // Bind methods
        this.start = this.start.bind(this);
        this.stop = this.stop.bind(this);
        this.negotiate = this.negotiate.bind(this);
        this.updateStatus = this.updateStatus.bind(this);
    }

    /**
     * Start the WebRTC connection
     */
    async start() {
        try {
            this.updateStatus('connecting', 'Connecting to camera...');
            
            // Make sure any previous connection is closed
            this.stop();
            
            // Wait a moment for resources to be released
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Get user media with basic constraints
            try {
                this.localStream = await navigator.mediaDevices.getUserMedia({
                    audio: false,
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        frameRate: { ideal: 30 }
                    }
                });
            } catch (err) {
                console.warn('Failed with ideal settings, trying basic video', err);
                this.localStream = await navigator.mediaDevices.getUserMedia({
                    audio: false,
                    video: true
                });
            }
            
            // Display local stream
            if (this.videoElement) {
                this.videoElement.srcObject = this.localStream;
            }
            
            // Create peer connection with simple config
            const config = {
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' }
                ]
            };
            
            this.pc = new RTCPeerConnection(config);
            
            // Add local stream tracks to peer connection
            this.localStream.getTracks().forEach(track => {
                this.pc.addTrack(track, this.localStream);
            });
            
            // Handle ICE connection state changes
            this.pc.addEventListener('iceconnectionstatechange', () => {
                if (this.pc.iceConnectionState === 'failed' || 
                    this.pc.iceConnectionState === 'closed') {
                    this.stop();
                }
            });
            
            // Handle data channel for messages
            this.pc.addEventListener('datachannel', (event) => {
                const channel = event.channel;
                
                channel.addEventListener('message', (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('Received data:', data);
                        
                        if (data.type === 'face_detected') {
                            this.onFaceDetected(data);
                            
                            // Update capture count for registration
                            if (this.mode === 'registration' && data.hasOwnProperty('capture_count')) {
                                this.captureCount = data.capture_count;
                            }
                        } else if (data.type === 'attendance_marked') {
                            this.onAttendanceMarked(data);
                        } else if (data.type === 'registration_complete') {
                            // Prevent multiple redirects
                            if (window.registrationRedirectInitiated) return;
                            
                            window.registrationRedirectInitiated = true;
                            this.stop();
                            this.onRegistrationComplete(data);
                            
                            // Redirect after delay
                            setTimeout(() => {
                                window.location.href = '/register?success=true';
                            }, 1500);
                        }
                    } catch (e) {
                        console.error('Error parsing message:', e);
                    }
                });
            });
            
            // Start negotiation
            await this.negotiate();
            
            this.updateStatus('connected', 'Connected to WebRTC server');
            
            // For registration mode, set up completion detection
            if (this.mode === 'registration') {
                this.setupRegistrationDetection();
            }
        } catch (error) {
            console.error('Error starting WebRTC:', error);
            this.updateStatus('error', `Error: ${error.message}`);
            this.stop();
        }
    }

    /**
     * Stop the WebRTC connection
     */
    stop() {
        // Stop all tracks
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }
        
        // Close peer connection
        if (this.pc) {
            this.pc.close();
            this.pc = null;
        }
        
        // Clear video element
        if (this.videoElement) {
            this.videoElement.srcObject = null;
        }
        
        // Notify server to release resources
        fetch('/release_webrtc', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                mode: this.mode,
                student_id: this.studentId
            })
        }).catch(err => console.warn('Error releasing resources:', err));
        
        this.updateStatus('disconnected', 'Disconnected');
    }

    /**
     * Set up detection of registration completion
     */
    setupRegistrationDetection() {
        const startTime = Date.now();
        
        const checkInterval = setInterval(() => {
            // Stop checking if video is gone or redirect initiated
            if (!this.videoElement || !this.videoElement.srcObject || window.registrationRedirectInitiated) {
                clearInterval(checkInterval);
                return;
            }
            
            const elapsedSeconds = (Date.now() - startTime) / 1000;
            
            // Force completion after timeout
            if (elapsedSeconds > 15) {
                clearInterval(checkInterval);
                if (window.registrationRedirectInitiated) return;
                
                window.registrationRedirectInitiated = true;
                this.stop();
                this.onRegistrationComplete({ success: true });
                
                setTimeout(() => {
                    window.location.href = '/register?success=true';
                }, 1500);
            }
            
            // Complete if we have enough captures
            if (this.captureCount >= 5) {
                clearInterval(checkInterval);
                if (window.registrationRedirectInitiated) return;
                
                window.registrationRedirectInitiated = true;
                this.stop();
                this.onRegistrationComplete({ success: true });
                
                setTimeout(() => {
                    window.location.href = '/register?success=true';
                }, 1500);
            }
        }, 500);
    }

    /**
     * Negotiate the WebRTC connection
     */
    async negotiate() {
        try {
            // Create offer
            const offer = await this.pc.createOffer();
            await this.pc.setLocalDescription(offer);
            
            // Wait for ICE gathering to complete or timeout
            await Promise.race([
                new Promise(resolve => {
                    const checkState = () => {
                        if (this.pc.iceGatheringState === 'complete') {
                            this.pc.removeEventListener('icegatheringstatechange', checkState);
                            resolve();
                        }
                    };
                    this.pc.addEventListener('icegatheringstatechange', checkState);
                    checkState();
                }),
                new Promise(resolve => setTimeout(resolve, 2000))
            ]);
            
            // Send offer to server
            const response = await fetch('/webrtc/offer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    sdp: this.pc.localDescription.sdp,
                    type: this.pc.localDescription.type,
                    mode: this.mode,
                    student_id: this.studentId
                })
            });
            
            // Get answer from server
            const answer = await response.json();
            if (answer.error) throw new Error(answer.error);
            
            // Set remote description
            await this.pc.setRemoteDescription(answer);
        } catch (error) {
            console.error('Negotiation error:', error);
            throw error;
        }
    }

    /**
     * Update status
     */
    updateStatus(status, message) {
        this.status = status;
        
        if (this.statusElement) {
            this.statusElement.textContent = message;
            this.statusElement.className = `status-${status}`;
        }
        
        this.onStatusChange(status, message);
    }
}

// Export the class
window.UnifiedWebRTCClient = UnifiedWebRTCClient;
