"""
Fresh implementation of the face recognition system.
This module provides functions for face detection, recognition, and attendance marking.
"""

import os
import sys
import logging
import numpy as np
import cv2
import time
from datetime import datetime, timezone
from typing import List, Dict, Tuple, Optional, Any, Union
import insightface
from insightface.app import FaceAnalysis
from sqlalchemy import func

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global variables
face_app = None
students_cache = None
last_cache_update = 0
CACHE_TIMEOUT = 60  # 60 seconds

# Import database models
from db import db, Student, Attendance

def initialize_insightface() -> Optional[FaceAnalysis]:
    """Initialize InsightFace face detector and recognition model.

    Returns:
        FaceAnalysis: Initialized InsightFace app, or None if initialization failed
    """
    global face_app

    # Return existing app if already initialized
    if face_app is not None:
        logger.info("InsightFace already initialized")
        return face_app

    try:
        # Initialize InsightFace
        face_app = FaceAnalysis(providers=['CPUExecutionProvider'])
        face_app.prepare(ctx_id=0, det_size=(640, 640))
        logger.info("Successfully initialized InsightFace")

        # Test the initialization
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        try:
            test_result = face_app.get(test_img)
            logger.info(f"InsightFace test: detected {len(test_result)} faces in test image")
        except Exception as e:
            logger.warning(f"InsightFace test failed (non-critical): {e}")

        return face_app
    except Exception as e:
        logger.error(f"Failed to initialize InsightFace: {e}")
        return None

def get_students_with_embeddings() -> List[Dict[str, Any]]:
    """Get all students with face embeddings from the database.

    Returns:
        List[Dict]: List of students with embeddings
    """
    global students_cache, last_cache_update

    # Return cached students if available and not expired
    current_time = time.time()
    if students_cache is not None and current_time - last_cache_update < CACHE_TIMEOUT:
        logger.info(f"Using cached students ({len(students_cache)} students)")
        return students_cache

    students = []
    try:
        # Query students with face embeddings
        db_students = Student.query.filter(
            (Student.face_embedding_array.isnot(None)) |
            (Student.face_embedding.isnot(None))
        ).all()

        for student in db_students:
            # Get embedding
            embedding = student.get_embedding()
            if embedding is not None:
                students.append({
                    'id': student.id,
                    'name': student.name,
                    'student_code': student.student_code,
                    'embedding': embedding
                })
                logger.info(f"Loaded embedding for student {student.name} (ID: {student.id})")
            else:
                logger.warning(f"Student {student.name} has no valid embedding")

        logger.info(f"Loaded {len(students)} students with embeddings")

        # Update cache
        students_cache = students
        last_cache_update = current_time

        return students
    except Exception as e:
        logger.error(f"Error getting students with embeddings: {e}")
        return []

def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    """Calculate cosine similarity between two vectors.

    Args:
        a: First vector
        b: Second vector

    Returns:
        float: Cosine similarity value
    """
    try:
        # Make sure both vectors are numpy arrays
        a = np.array(a)
        b = np.array(b)

        # Normalize vectors if they're not already normalized
        a_norm = np.linalg.norm(a)
        if a_norm > 0:
            a = a / a_norm

        b_norm = np.linalg.norm(b)
        if b_norm > 0:
            b = b / b_norm

        # Calculate cosine similarity
        similarity = np.dot(a, b)

        return similarity
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {e}")
        return 0.0

def extract_face_embedding(frame: np.ndarray) -> Optional[np.ndarray]:
    """Extract face embedding from a frame.

    Args:
        frame: Input frame

    Returns:
        np.ndarray: Face embedding, or None if no face detected
    """
    try:
        # Initialize InsightFace if not already initialized
        global face_app
        if face_app is None:
            face_app = initialize_insightface()

        if face_app is None:
            logger.error("InsightFace not initialized")
            return None

        # Detect faces
        faces = face_app.get(frame)

        # Check if any faces detected
        if not faces:
            logger.info("No faces detected")
            return None

        # Get the largest face
        largest_face = max(faces, key=lambda face:
                          (face.bbox[2] - face.bbox[0]) * (face.bbox[3] - face.bbox[1]))

        # Get face embedding
        if hasattr(largest_face, 'embedding') and largest_face.embedding is not None:
            # Normalize embedding
            embedding = largest_face.embedding / np.linalg.norm(largest_face.embedding)
            logger.info("Successfully extracted face embedding")
            return embedding
        else:
            logger.warning("Face detected but no embedding available")
            return None
    except Exception as e:
        logger.error(f"Error extracting face embedding: {e}")
        return None

def recognize_face(embedding: np.ndarray, threshold: float = 0.01) -> Optional[Dict[str, Any]]:
    """Recognize a face by comparing its embedding with stored embeddings.

    Args:
        embedding: Face embedding
        threshold: Similarity threshold

    Returns:
        Dict: Matched student, or None if no match found
    """
    try:
        # Get students with embeddings
        students = get_students_with_embeddings()

        if not students:
            logger.warning("No students with embeddings found")
            return None

        # Find best match
        best_match = None
        best_similarity = 0

        for student in students:
            # Calculate similarity
            similarity = cosine_similarity(embedding, student['embedding'])
            logger.info(f"Similarity with student {student['name']}: {similarity:.4f}")

            if similarity > threshold and similarity > best_similarity:
                best_similarity = similarity
                best_match = student.copy()  # Copy to avoid modifying the cache
                best_match['similarity'] = similarity

        if best_match:
            logger.info(f"Matched student: {best_match['name']} with similarity {best_match['similarity']:.4f}")
            return best_match
        else:
            logger.info(f"No match found (threshold: {threshold})")
            return None
    except Exception as e:
        logger.error(f"Error recognizing face: {e}")
        return None

def mark_attendance(student_id: int, confidence: float) -> bool:
    """Mark attendance for a student.

    Args:
        student_id: Student ID
        confidence: Confidence score

    Returns:
        bool: True if attendance marked successfully, False otherwise
    """
    try:
        # Check if attendance already marked today
        today = datetime.now(timezone.utc).date()
        existing = Attendance.query.filter(
            Attendance.student_id == student_id,
            func.date(Attendance.timestamp) == today
        ).first()

        if existing:
            logger.info(f"Attendance already marked for student {student_id} today")
            return False

        # Get student information
        student = Student.query.get(student_id)

        # Mark attendance
        attendance = Attendance(
            student_id=student_id,
            timestamp=datetime.now(timezone.utc),
            confidence_score=float(confidence),
            student_code=student.student_code if student else None,
            student_name=student.name if student else None,
            student_department=student.department if student else None
        )
        db.session.add(attendance)
        db.session.commit()
        logger.info(f"Successfully marked attendance for student {student_id} with confidence {confidence:.4f}")
        return True
    except Exception as e:
        logger.error(f"Error marking attendance: {e}")
        db.session.rollback()
        return False

def process_frame(frame: np.ndarray) -> Tuple[Optional[Dict[str, Any]], Optional[np.ndarray]]:
    """Process a frame for face recognition and attendance marking.

    Args:
        frame: Input frame

    Returns:
        Tuple: (Matched student, Face embedding) or (None, None) if no match found
    """
    try:
        # Extract face embedding
        embedding = extract_face_embedding(frame)

        if embedding is None:
            return None, None

        # Recognize face
        matched_student = recognize_face(embedding)

        if matched_student:
            # Mark attendance
            success = mark_attendance(matched_student['id'], matched_student['similarity'])

            if success:
                logger.info(f"Marked attendance for student {matched_student['name']}")
            else:
                logger.info(f"Failed to mark attendance for student {matched_student['name']}")

            return matched_student, embedding
        else:
            return None, embedding
    except Exception as e:
        logger.error(f"Error processing frame: {e}")
        return None, None
