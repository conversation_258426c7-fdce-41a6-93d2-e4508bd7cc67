import cv2
import numpy as np
import onnxruntime
from pathlib import Path
import logging
from typing import Tuple, Optional, List, Dict
from datetime import datetime
import threading
import os
from ..utils.logger import face_logger
from ..utils.monitoring import system_monitor
from flask import current_app
from ..extensions import cache

logger = logging.getLogger(__name__)

class FaceRecognitionService:
    """Face recognition service with caching and error handling."""
    
    def __init__(self, model_path: str = None):
        """Initialize the face recognition service.
        
        Args:
            model_path (str): Path to the ONNX model file
        """
        if model_path is None:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            model_path = os.path.join(base_dir, 'models', 'face_recognition.onnx')
        self.model_path = model_path
        self.session = None
        self.initialize_model()
    
    def initialize_model(self):
        """Initialize the ONNX model."""
        try:
            if not Path(self.model_path).exists():
                logger.warning(f"Model file not found at {self.model_path}. Using default model.")
                # Here you would typically download a pre-trained model
                # For now, we'll create a dummy model file
                self._create_dummy_model()
            
            self.session = onnxruntime.InferenceSession(
                self.model_path,
                providers=['CPUExecutionProvider']
            )
            logger.info("Face recognition model initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize face recognition model: {str(e)}")
            raise
    
    def _create_dummy_model(self):
        """Create a dummy ONNX model for testing purposes."""
        try:
            # Create a simple model that returns random embeddings
            import onnx
            from onnx import helper, numpy_helper
            
            # Create a simple model with one input and one output
            X = helper.make_tensor_value_info('input', onnx.TensorProto.FLOAT, [1, 3, 112, 112])
            Y = helper.make_tensor_value_info('output', onnx.TensorProto.FLOAT, [1, 512])
            
            # Create a simple graph
            node = helper.make_node(
                'RandomUniform',
                inputs=[],
                outputs=['output'],
                name='random_node',
                high=1.0,
                low=-1.0,
                shape=[1, 512]
            )
            
            # Create the graph
            graph = helper.make_graph(
                [node],
                'dummy_model',
                [],
                [Y]
            )
            
            # Create the model
            model = helper.make_model(graph)
            model.opset_import[0].version = 11
            
            # Save the model
            Path(self.model_path).parent.mkdir(parents=True, exist_ok=True)
            onnx.save(model, self.model_path)
            logger.info(f"Created dummy model at {self.model_path}")
        except Exception as e:
            logger.error(f"Failed to create dummy model: {str(e)}")
            raise
    
    def detect_face(self, image: np.ndarray) -> tuple:
        """Detect face in the image.
        
        Args:
            image (np.ndarray): Input image
            
        Returns:
            tuple: (x, y, w, h) coordinates of the detected face
        """
        try:
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.3, 5)
            
            if len(faces) == 0:
                return None
            
            # Return the largest face
            return max(faces, key=lambda x: x[2] * x[3])
        except Exception as e:
            logger.error(f"Face detection failed: {str(e)}")
            return None
    
    def get_face_embedding(self, face_image: np.ndarray) -> np.ndarray:
        """Get face embedding from the face image.
        
        Args:
            face_image (np.ndarray): Face image
            
        Returns:
            np.ndarray: Face embedding vector
        """
        try:
            # Preprocess the image
            face_image = cv2.resize(face_image, (112, 112))
            face_image = face_image.transpose(2, 0, 1)
            face_image = np.expand_dims(face_image, axis=0)
            face_image = (face_image - 127.5) / 128.0
            
            # Get embedding
            output_name = self.session.get_outputs()[0].name
            embedding = self.session.run([output_name], {})[0]
            
            return embedding[0]
        except Exception as e:
            logger.error(f"Failed to get face embedding: {str(e)}")
            return None
    
    def compare_faces(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """Compare two face embeddings.
        
        Args:
            embedding1 (np.ndarray): First face embedding
            embedding2 (np.ndarray): Second face embedding
            
        Returns:
            float: Similarity score between 0 and 1
        """
        try:
            # Normalize embeddings
            embedding1 = embedding1 / np.linalg.norm(embedding1)
            embedding2 = embedding2 / np.linalg.norm(embedding2)
            
            # Calculate cosine similarity
            similarity = np.dot(embedding1, embedding2)
            return float(similarity)
        except Exception as e:
            logger.error(f"Face comparison failed: {str(e)}")
            return 0.0
    
    def verify_face(self, image: np.ndarray, reference_embedding: np.ndarray, threshold: float = 0.6) -> bool:
        """Verify if the face in the image matches the reference embedding.
        
        Args:
            image (np.ndarray): Input image
            reference_embedding (np.ndarray): Reference face embedding
            threshold (float): Similarity threshold
            
        Returns:
            bool: True if faces match, False otherwise
        """
        try:
            # Detect face
            face = self.detect_face(image)
            if face is None:
                return False
            
            x, y, w, h = face
            face_image = image[y:y+h, x:x+w]
            
            # Get face embedding
            face_embedding = self.get_face_embedding(face_image)
            if face_embedding is None:
                return False
            
            # Compare embeddings
            similarity = self.compare_faces(face_embedding, reference_embedding)
            return similarity >= threshold
        except Exception as e:
            logger.error(f"Face verification failed: {str(e)}")
            return False

# Create global service instance
face_recognition_service = FaceRecognitionService() 