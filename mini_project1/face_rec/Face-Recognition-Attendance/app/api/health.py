from flask import Blueprint, jsonify
from ..utils.monitoring import system_monitor
from ..services.face_recognition import face_recognition_service
from ..extensions import db
import psutil
import os

health_bp = Blueprint('health', __name__)

@health_bp.route('/health')
def health_check():
    """Check system health and return status."""
    try:
        # Check database connection
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except Exception as e:
        db_status = 'unhealthy'
    
    # Check face recognition model
    try:
        face_recognition_service._initialize_model()
        model_status = 'healthy'
    except Exception as e:
        model_status = 'unhealthy'
    
    # Get system metrics
    cpu_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # Check if required directories exist
    required_dirs = ['uploads', 'logs', 'dataset', 'training']
    dir_status = {
        dir_name: os.path.exists(os.path.join(os.getcwd(), dir_name))
        for dir_name in required_dirs
    }
    
    # Overall system status
    system_health = system_monitor.get_health()
    
    return jsonify({
        'status': 'healthy' if all([
            db_status == 'healthy',
            model_status == 'healthy',
            cpu_percent < 90,
            memory.percent < 90,
            disk.percent < 90,
            all(dir_status.values())
        ]) else 'degraded',
        'components': {
            'database': db_status,
            'face_recognition_model': model_status,
            'directories': dir_status
        },
        'metrics': {
            'cpu_usage': cpu_percent,
            'memory_usage': memory.percent,
            'disk_usage': disk.percent,
            'request_count': system_health['metrics']['request_count'],
            'error_count': system_health['metrics']['error_count']
        },
        'uptime': system_health['uptime']
    }) 