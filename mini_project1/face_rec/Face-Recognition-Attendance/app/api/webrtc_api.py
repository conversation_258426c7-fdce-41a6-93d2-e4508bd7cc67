"""
WebRTC API endpoints for the Face Recognition Attendance System.
"""

import os
import json
import time
from datetime import datetime, timezone
from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required
from sqlalchemy import desc

from db import db, Student, Attendance

# Create blueprint
webrtc_api = Blueprint('webrtc_api', __name__, url_prefix='/api')

@webrtc_api.route('/students', methods=['GET'])
def get_students():
    """Get all students with face embeddings for WebRTC server."""
    try:
        # Query students with face embeddings
        students = Student.query.filter(Student.face_embedding.isnot(None)).all()
        
        # Format response
        result = []
        for student in students:
            # Convert face embedding to list if it's not already
            embedding = student.get_embedding()
            if embedding is not None:
                embedding = embedding.tolist()
            
            result.append({
                'id': student.id,
                'student_code': student.student_code,
                'name': student.name,
                'department': student.department,
                'face_embedding': embedding
            })
        
        return jsonify(result)
    except Exception as e:
        current_app.logger.error(f"Error getting students: {e}")
        return jsonify({'error': str(e)}), 500

@webrtc_api.route('/mark_attendance', methods=['POST'])
def mark_attendance():
    """Mark attendance from WebRTC server with time window logic."""
    try:
        # Get request data
        data = request.json
        student_id = data.get('student_id')
        confidence = data.get('confidence')
        device_id = data.get('device_id', 'webrtc')
        
        # Validate required fields
        if not student_id:
            return jsonify({'error': 'Missing student_id'}), 400
        
        # Get student
        student = Student.query.get(student_id)
        if not student:
            return jsonify({'error': f'Student with ID {student_id} not found'}), 404
        
        # Mark attendance with time window logic
        now = datetime.now(timezone.utc)
        
        # Check for existing attendance within the time window
        time_window_minutes = int(os.getenv('ATTENDANCE_TIME_WINDOW', '10'))
        is_new, attendance = Attendance.mark_attendance(
            db.session, 
            student_id, 
            confidence=confidence, 
            device_id=device_id,
            min_interval_minutes=time_window_minutes
        )
        
        if is_new:
            message = f'Attendance marked for {student.name}'
            current_app.logger.info(f"{message} (ID: {student_id}, confidence: {confidence:.2f if confidence else 'N/A'})")
        else:
            message = f'Duplicate attendance for {student.name} - last marked at {attendance.timestamp}'
            current_app.logger.info(message)
        
        # Emit event via SocketIO if available
        if hasattr(current_app, 'socketio'):
            current_app.socketio.emit('attendance_result', {
                'student_id': student_id,
                'name': student.name,
                'timestamp': now.isoformat(),
                'status': 'present',
                'confidence': f"{confidence:.2f}" if confidence else None,
                'device_id': device_id,
                'is_new': is_new
            })
        
        return jsonify({
            'status': 'success',
            'message': message,
            'timestamp': now.isoformat(),
            'name': student.name,
            'is_new': is_new
        })
    except Exception as e:
        current_app.logger.error(f"Error marking attendance: {e}")
        return jsonify({'error': str(e)}), 500

@webrtc_api.route('/webrtc_status', methods=['GET'])
def webrtc_status():
    """Get WebRTC server status."""
    try:
        import requests
        webrtc_url = os.getenv('WEBRTC_SERVER_URL', 'http://localhost:8080')
        
        try:
            response = requests.get(f"{webrtc_url}/stats", timeout=2.0)
            if response.status_code == 200:
                return jsonify({
                    'status': 'online',
                    'stats': response.json()
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': f"WebRTC server returned status code {response.status_code}"
                })
        except requests.exceptions.RequestException:
            return jsonify({
                'status': 'offline',
                'message': "WebRTC server is not reachable"
            })
    except Exception as e:
        current_app.logger.error(f"Error checking WebRTC status: {e}")
        return jsonify({'error': str(e)}), 500
