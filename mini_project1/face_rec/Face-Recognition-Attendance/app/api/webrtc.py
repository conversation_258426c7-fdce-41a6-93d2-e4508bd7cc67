"""
WebRTC API endpoints for the Face Recognition Attendance System.
"""

import os
import json
import time
from datetime import datetime, timezone
from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required
from sqlalchemy import desc

from db import db, Student, Attendance

# Create blueprint
webrtc_bp = Blueprint('webrtc', __name__, url_prefix='/api')

@webrtc_bp.route('/students', methods=['GET'])
def get_students():
    """Get all students with face embeddings for WebRTC server."""
    try:
        # Query students with face embeddings
        students = Student.query.filter(Student.face_embedding.isnot(None)).all()

        # Format response
        result = []
        for student in students:
            # Convert face embedding to list if it's not already
            embedding = student.get_embedding()
            if embedding is not None:
                embedding = embedding.tolist()

            result.append({
                'id': student.id,
                'student_code': student.student_code,
                'name': student.name,
                'department': student.department,
                'face_embedding': embedding
            })

        return jsonify(result)
    except Exception as e:
        current_app.logger.error(f"Error getting students: {e}")
        return jsonify({'error': str(e)}), 500

@webrtc_bp.route('/mark_attendance', methods=['POST'])
def mark_attendance():
    """Mark attendance from WebRTC server."""
    try:
        # Get request data
        data = request.json
        student_id = data.get('student_id')
        confidence = data.get('confidence')
        device_id = data.get('device_id', 'webrtc')

        # Validate required fields
        if not student_id:
            return jsonify({'error': 'Missing student_id'}), 400

        # Get current time in UTC
        now = datetime.now(timezone.utc)

        # Check if already marked within time window
        time_window_minutes = int(os.getenv('ATTENDANCE_TIME_WINDOW', '10'))
        time_window_seconds = time_window_minutes * 60

        # Get the latest attendance record for this student
        latest_attendance = Attendance.query.filter(
            Attendance.student_id == student_id
        ).order_by(desc(Attendance.timestamp)).first()

        # Check if within time window
        if latest_attendance and (now - latest_attendance.timestamp.replace(tzinfo=timezone.utc)).total_seconds() < time_window_seconds:
            current_app.logger.info(f"Attendance already marked for student ID {student_id} within time window")
            return jsonify({
                'status': 'duplicate',
                'message': 'Attendance already marked within time window',
                'last_marked': latest_attendance.timestamp.isoformat()
            })

        # Get student information
        student = Student.query.get(student_id)

        # Create new attendance record
        attendance = Attendance(
            student_id=student_id,
            timestamp=now,
            confidence_score=confidence,
            device_id=device_id,
            student_code=student.student_code if student else None,
            student_name=student.name if student else None,
            student_department=student.department if student else None
        )
        db.session.add(attendance)
        db.session.commit()

        # Get student name
        student = Student.query.get(student_id)
        student_name = student.name if student else f"Student {student_id}"

        current_app.logger.info(f"Marked attendance for {student_name} (ID: {student_id}, confidence: {confidence:.2f if confidence else 'N/A'})")

        # Emit event via SocketIO if available
        if hasattr(current_app, 'socketio'):
            current_app.socketio.emit('attendance_result', {
                'student_id': student_id,
                'name': student_name,
                'timestamp': now.isoformat(),
                'status': 'present',
                'confidence': f"{confidence:.2f}" if confidence else None,
                'device_id': device_id
            })

        return jsonify({
            'status': 'success',
            'message': f'Attendance marked for {student_name}',
            'timestamp': now.isoformat()
        })
    except Exception as e:
        current_app.logger.error(f"Error marking attendance: {e}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@webrtc_bp.route('/webrtc_status', methods=['GET'])
def webrtc_status():
    """Get WebRTC server status."""
    try:
        import requests
        webrtc_url = os.getenv('WEBRTC_SERVER_URL', 'http://localhost:8080')

        try:
            response = requests.get(f"{webrtc_url}/stats", timeout=2.0)
            if response.status_code == 200:
                return jsonify({
                    'status': 'online',
                    'stats': response.json()
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': f"WebRTC server returned status code {response.status_code}"
                })
        except requests.exceptions.RequestException:
            return jsonify({
                'status': 'offline',
                'message': "WebRTC server is not reachable"
            })
    except Exception as e:
        current_app.logger.error(f"Error checking WebRTC status: {e}")
        return jsonify({'error': str(e)}), 500
