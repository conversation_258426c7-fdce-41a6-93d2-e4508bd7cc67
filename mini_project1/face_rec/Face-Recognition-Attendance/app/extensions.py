from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from flask_migrate import Migrate
from flask_talisman import Talisman
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_caching import Cache

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
talisman = Talisman()
limiter = Limiter(key_func=get_remote_address)
cache = Cache() 