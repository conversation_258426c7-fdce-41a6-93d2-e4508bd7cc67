import time
import psutil
import threading
from typing import Dict, Any
from datetime import datetime
from .logger import app_logger

class SystemMonitor:
    """System monitoring and health check utilities."""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_usage': [],
            'request_count': 0,
            'error_count': 0,
            'last_error': None
        }
        self._start_monitoring()
    
    def _start_monitoring(self):
        """Start background monitoring thread."""
        def monitor():
            while True:
                try:
                    self.metrics['cpu_usage'].append(psutil.cpu_percent())
                    self.metrics['memory_usage'].append(psutil.virtual_memory().percent)
                    self.metrics['disk_usage'].append(psutil.disk_usage('/').percent)
                    
                    # Keep only last 100 measurements
                    for key in ['cpu_usage', 'memory_usage', 'disk_usage']:
                        if len(self.metrics[key]) > 100:
                            self.metrics[key] = self.metrics[key][-100:]
                    
                    time.sleep(60)  # Update every minute
                except Exception as e:
                    app_logger.error(f"Monitoring error: {str(e)}")
                    time.sleep(60)
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
    
    def record_request(self, success: bool = True):
        """Record a request."""
        self.metrics['request_count'] += 1
        if not success:
            self.metrics['error_count'] += 1
    
    def record_error(self, error: Exception):
        """Record an error."""
        self.metrics['last_error'] = {
            'timestamp': datetime.now().isoformat(),
            'error': str(error)
        }
        self.metrics['error_count'] += 1
    
    def get_health(self) -> Dict[str, Any]:
        """Get system health status."""
        return {
            'status': 'healthy' if self.metrics['error_count'] < 10 else 'degraded',
            'uptime': time.time() - self.start_time,
            'metrics': {
                'cpu_usage': self.metrics['cpu_usage'][-1] if self.metrics['cpu_usage'] else 0,
                'memory_usage': self.metrics['memory_usage'][-1] if self.metrics['memory_usage'] else 0,
                'disk_usage': self.metrics['disk_usage'][-1] if self.metrics['disk_usage'] else 0,
                'request_count': self.metrics['request_count'],
                'error_count': self.metrics['error_count'],
                'last_error': self.metrics['last_error']
            }
        }

# Create global monitor instance
system_monitor = SystemMonitor() 