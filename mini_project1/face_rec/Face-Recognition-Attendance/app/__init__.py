"""
Face Recognition Attendance System - Flask Application
"""

import os
import logging
from flask import Flask
from flask_socketio import So<PERSON><PERSON>
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_talisman import Talisman
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_caching import Cache
from logging.handlers import RotatingFileHandler
from pathlib import Path
from loguru import logger as loguru_logger
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize extensions
socketio = SocketIO(cors_allowed_origins="*")
login_manager = LoginManager()
migrate = Migrate()
talisman = Talisman()
limiter = Limiter(key_func=get_remote_address)
cache = Cache()

def create_app(config_name=None):
    """Create and configure the Flask application."""
    app = Flask(__name__)

    # Load configuration
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')

    # Load configuration from environment variables
    app.config.from_mapping(
        SECRET_KEY=os.getenv('FLASK_SECRET_KEY', 'default_secret_key'),
        SQLALCHEMY_DATABASE_URI=f"postgresql://{os.getenv('DB_USER', 'postgres')}:{os.getenv('DB_PASS', 'postgres')}@{os.getenv('DB_HOST', 'localhost')}:{os.getenv('DB_PORT', '5432')}/{os.getenv('DB_NAME', 'attendance_db')}",
        SQLALCHEMY_TRACK_MODIFICATIONS=False,
        FACE_RECOGNITION_THRESHOLD=float(os.getenv('FACE_RECOGNITION_THRESHOLD', '0.6')),
        FACE_DETECTION_SIZE=int(os.getenv('FACE_DETECTION_SIZE', '640')),
        FACE_CACHE_SIZE=int(os.getenv('FACE_CACHE_SIZE', '1000')),
        FRAME_SKIP=int(os.getenv('FRAME_SKIP', '3')),
        USE_GPU=os.getenv('USE_GPU', 'True').lower() in ('true', '1', 't'),
        VERIFICATION_THRESHOLD=float(os.getenv('VERIFICATION_THRESHOLD', '0.1')),
        ATTENDANCE_TIME_WINDOW=int(os.getenv('ATTENDANCE_TIME_WINDOW', '10')),
        LOG_FOLDER=os.getenv('LOG_FOLDER', 'logs'),
    )

    # Try to load from config module if it exists
    try:
        from .config import config
        app.config.from_object(config[config_name])
        config[config_name].init_app(app)
    except (ImportError, KeyError):
        pass

    # Initialize extensions
    from db import db
    db.init_app(app)

    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    migrate.init_app(app, db)
    cache.init_app(app)
    socketio.init_app(app)

    # Configure security headers
    if os.getenv("FLASK_ENV", "development") == "production":
        talisman.init_app(
            app,
            force_https=True,
            strict_transport_security=True,
            session_cookie_secure=True,
            content_security_policy=None
        )
    else:
        talisman.init_app(
            app,
            force_https=False,
            strict_transport_security=False,
            session_cookie_secure=False,
            content_security_policy=None
        )

    # Configure rate limiting
    limiter.init_app(app)

    # Configure logging
    if not app.debug and not app.testing:
        if not os.path.exists(app.config['LOG_FOLDER']):
            os.makedirs(app.config['LOG_FOLDER'], exist_ok=True)
        file_handler = RotatingFileHandler(
            os.path.join(app.config['LOG_FOLDER'], 'app.log'),
            maxBytes=10240000,
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

        app.logger.setLevel(logging.INFO)
        app.logger.info('Face Recognition Attendance startup')

    # Configure Loguru
    loguru_logger.add(
        os.path.join(app.config['LOG_FOLDER'], "app_{time}.log"),
        rotation="10 MB",
        retention="1 week",
        level="INFO",
        backtrace=True,
        diagnose=True
    )

    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('dataset', exist_ok=True)
    os.makedirs('training', exist_ok=True)
    os.makedirs('temp', exist_ok=True)
    os.makedirs('models', exist_ok=True)

    # Register blueprints
    try:
        from .api import api as api_blueprint
        app.register_blueprint(api_blueprint, url_prefix='/api')
    except ImportError:
        pass

    try:
        from .main import main as main_blueprint
        app.register_blueprint(main_blueprint)
    except ImportError:
        pass

    # Register WebRTC API blueprint
    from app.api.webrtc_api import webrtc_api
    app.register_blueprint(webrtc_api)

    # Ensure database exists and create tables
    with app.app_context():
        from db import ensure_database_exists, create_tables_if_missing
        ensure_database_exists()
        create_tables_if_missing()

    # User loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        from db import Admin
        return Admin.query.get(int(user_id))

    # Root route
    @app.route('/')
    def index():
        return 'Face Recognition Attendance System'

    return app
