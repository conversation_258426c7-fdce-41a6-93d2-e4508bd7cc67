import asyncio
import cv2
import numpy as np
import json
import os
import time
import logging
import traceback
import gc
from aiohttp import web
from aiohttp_cors import setup as setup_cors, ResourceOptions
from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack
from aiortc.mediastreams import MediaStreamError
from aiortc.contrib.media import MediaBlackhole, MediaRelay
import requests
from dotenv import load_dotenv
from loguru import logger
import threading
import queue
import uuid
import psutil
from typing import Dict, Set, Optional, Tuple, List, Any
from pathlib import Path

# Import face recognition utilities
from face_utils import (
    preprocess_image,
    cosine_similarity,
    start_background_processing,
    stop_background_processing,
    app as insight_app,
    FACE_RECOGNITION_THRESHOLD
)

# Get the absolute path to the .env file
env_path = Path(__file__).parent / '.env'
load_dotenv(dotenv_path=env_path)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger.add(
    "logs/webrtc_{time}.log",
    rotation="10 MB",
    retention="1 week",
    level="INFO",
    backtrace=True,
    diagnose=True
)

# Configuration
FLASK_API_URL = 'http://localhost:5001'  # Hardcoded to match Flask server port
logger.info(f"FLASK_API_URL at startup: {FLASK_API_URL}")
FRAME_SKIP = int(os.getenv('FRAME_SKIP', '3'))  # Reduced to process more frames for smoother video
USE_GPU = os.getenv('USE_GPU', 'True').lower() in ('true', '1', 't')
VERIFICATION_THRESHOLD = float(os.getenv('VERIFICATION_THRESHOLD', '0.1'))
ATTENDANCE_TIME_WINDOW = int(os.getenv('ATTENDANCE_TIME_WINDOW', '10'))
MAX_QUEUE_SIZE = int(os.getenv('MAX_QUEUE_SIZE', '3'))  # Further reduced queue size for faster processing
RESOURCE_LOG_INTERVAL = int(os.getenv('RESOURCE_LOG_INTERVAL', '300'))  # Log resource usage every 5 minutes
DETECTION_SCALE = float(os.getenv('DETECTION_SCALE', '0.3'))  # Reduced scale factor for faster detection
CONNECTION_TIMEOUT = int(os.getenv('CONNECTION_TIMEOUT', '5'))  # Reduced connection timeout for faster response
MAX_FPS = int(os.getenv('MAX_FPS', '30'))  # Increased maximum FPS for smoother video

# Global variables
pcs = set()
relay = MediaRelay()
frame_counter = 0
last_attendance_time = {}
active_connections = {}
processing_queue = queue.Queue(maxsize=MAX_QUEUE_SIZE)
processing_thread = None
processing_active = False
last_resource_log = time.time()
student_cache = {}  # Cache for student embeddings
student_embeddings = {}  # Store student embeddings for immediate use
registration_complete = {}  # Track registration completion status

# Helper function to send registration complete message
async def send_registration_complete(data_channel, student_id):
    """Send registration complete message via data channel."""
    try:
        if data_channel and data_channel.readyState == 'open':
            message = json.dumps({
                'type': 'registration_complete',
                'success': True,
                'message': 'Face registration complete',
                'student_id': student_id,
                'capture_count': 5  # Add capture count to indicate completion
            })

            # Send only once to avoid multiple success messages
            await data_channel.send(message)

            # Small delay to ensure message is processed
            await asyncio.sleep(0.1)

            logger.info(f"Sent registration complete message for student {student_id}")
            return True
        else:
            logger.warning(f"Data channel not open for student {student_id}")
            return False
    except Exception as e:
        logger.error(f"Error sending registration complete message: {str(e)[:100]}")
        return False

# Start background processing with optimized performance
def start_processing():
    global processing_thread, processing_active

    if processing_active:
        return

    def process_queue():
        """Background thread for processing frames from the queue"""
        global processing_active
        while processing_active:
            try:
                # Get frame from queue
                frame_data = processing_queue.get(timeout=1.0)
                if frame_data is None:
                    continue

                pc, frame, is_registration, student_id = frame_data

                if is_registration:
                    # Handle registration mode
                    try:
                        # Process the face for registration
                        if not hasattr(pc, 'captured_faces'):
                            pc.captured_faces = []
                            pc.registration_complete = False

                        # Extract face embedding
                        h, w = frame.shape[:2]
                        small_frame = cv2.resize(frame, (int(w * DETECTION_SCALE), int(h * DETECTION_SCALE)))
                        processed_frame = preprocess_image(small_frame)
                        faces = insight_app.get(processed_frame)

                        if faces and len(faces) == 1:
                            # Get the face embedding
                            face = faces[0]
                            embedding = face.embedding

                            if embedding is not None:
                                # Add to captured faces
                                pc.captured_faces.append(embedding)
                                logger.info(f"Captured face {len(pc.captured_faces)}/5")

                                # If we have enough faces, register the student
                                if len(pc.captured_faces) >= 5 and not pc.registration_complete:
                                    try:
                                        # Average the embeddings
                                        avg_embedding = np.mean(pc.captured_faces, axis=0)

                                        # Save to database via API
                                        headers = {
                                            'Content-Type': 'application/json'
                                        }

                                        logger.info(f"Attempting to POST embedding to {FLASK_API_URL}/api/register_face for student {student_id}")
                                        response = requests.post(
                                            f"{FLASK_API_URL}/api/register_face",
                                            json={
                                                "student_id": student_id,
                                                "embedding": avg_embedding.tolist()
                                            },
                                            headers=headers,
                                            timeout=5.0
                                        )

                                        if response.status_code == 200:
                                            logger.info(f"Successfully registered face for student {student_id}")
                                            pc.registration_complete = True
                                        else:
                                            logger.error(f"Failed to register face: {response.text}")
                                    except Exception as e:
                                        logger.error(f"Error processing registration frame: {e}")

                    except Exception as e:
                        logger.error(f"Error processing registration frame: {e}")

                else:
                    # Process frame for attendance recognition
                    result = process_frame_for_recognition(frame)
                    if result:
                        student_id, confidence = result

                    # Mark attendance via Flask API
                    try:
                        response = requests.post(
                            f"{FLASK_API_URL}/api/mark_attendance",
                            json={
                                "student_id": student_id,
                                "confidence": float(confidence),
                                "device_id": student_id
                            },
                            timeout=3.0  # Increased timeout for better reliability
                        )
                        if response.status_code == 200:
                            resp_data = response.json()
                            student_name = resp_data.get('name', f"Student {student_id}")

                            # Store last attendance time to prevent duplicates
                            last_attendance_time[student_id] = time.time()

                            # Log attendance (reduced logging)
                            logger.info(f"Marked attendance for student ID {student_id} with confidence {confidence:.4f}")
                        else:
                            # Reduced logging for failures
                            if frame_counter % 10 == 0:  # Log only every 10th failure
                                logger.warning(f"Failed to mark attendance: {response.status_code}")
                    except Exception as e:
                        # Reduced error logging
                        if frame_counter % 10 == 0:  # Log only every 10th error
                            logger.error(f"Error marking attendance via API: {str(e)[:100]}")  # Truncate long error messages

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in process_queue: {e}")
                continue

    # Start the processing thread
    processing_active = True
    processing_thread = threading.Thread(target=process_queue, daemon=True)
    processing_thread.start()
    logger.info("Started background processing thread")

def stop_processing():
    """Stop the background processing thread"""
    global processing_active

    if not processing_active:
        return

    logger.info("Stopping background processing thread...")
    processing_active = False

    if processing_thread:
        # Wait for thread to finish with timeout
        processing_thread.join(timeout=2.0)
        if processing_thread.is_alive():
            logger.warning("Background processing thread did not terminate gracefully")
        else:
            logger.info("Background processing thread stopped successfully")

    # Clear the queue
    try:
        while not processing_queue.empty():
            processing_queue.get_nowait()
            processing_queue.task_done()
    except Exception:
        pass

def process_frame_for_recognition(frame: np.ndarray, connection_id: str = None) -> Optional[Tuple[int, float]]:
    """Process a frame for face recognition with optimized performance.

    Args:
        frame: Input frame as numpy array
        connection_id: ID of the connection for registration mode

    Returns:
        Tuple of (student_id, confidence) or None if no face recognized
    """
    global student_cache, last_resource_log

    try:
        # Log resource usage less frequently
        current_time = time.time()
        if current_time - last_resource_log > RESOURCE_LOG_INTERVAL:
            cpu_percent = psutil.cpu_percent()
            memory_info = psutil.virtual_memory()
            logger.info(f"Resource usage - CPU: {cpu_percent}%, Memory: {memory_info.percent}%")
            last_resource_log = current_time

        if frame is None:
            return None

        # Resize frame for faster processing
        h, w = frame.shape[:2]
        small_frame = cv2.resize(frame, (int(w * DETECTION_SCALE), int(h * DETECTION_SCALE)))

        # Preprocess image for better detection
        processed_frame = preprocess_image(small_frame)

        # Detect faces
        faces = insight_app.get(processed_frame)
        if not faces:
            return None

        # Get the largest face if multiple faces detected
        if len(faces) > 1:
            largest_face = max(faces, key=lambda face:
                              (face.bbox[2] - face.bbox[0]) * (face.bbox[3] - face.bbox[1]))
            face = largest_face
        else:
            face = faces[0]

        # Get embedding
        embedding = face.embedding

        # Check if we need to refresh the student cache (less frequently)
        if not student_cache or current_time - student_cache.get('last_updated', 0) > 300:  # Refresh every 5 minutes
            try:
                response = requests.get(f"{FLASK_API_URL}/api/students", timeout=3.0)
                if response.status_code != 200:
                    # Use existing cache if available
                    if not student_cache:
                        return None
                else:
                    # Update cache
                    students = response.json()
                    student_cache = {
                        'students': students,
                        'last_updated': current_time
                    }
                    logger.info(f"Updated student cache with {len(students)} students")
            except Exception as e:
                # Use existing cache if available
                if not student_cache:
                    return None

        # Get students from cache
        students = student_cache.get('students', [])
        if not students:
            return None

        # Find best match (optimized)
        best_sim = -1
        best_student_id = None
        second_best_sim = -1
        best_student_name = None

        # Process students in batches for better performance
        for student in students:
            try:
                # Get student embedding
                student_embedding = np.array(student.get('face_embedding'))
                if student_embedding is None or len(student_embedding) == 0:
                    continue

                # Calculate similarity
                sim = cosine_similarity(embedding, student_embedding)

                # Track best and second-best matches
                if sim > best_sim:
                    second_best_sim = best_sim
                    best_sim = sim
                    best_student_id = student.get('id')
                    best_student_name = student.get('name')
                elif sim > second_best_sim:
                    second_best_sim = sim
            except Exception:
                continue

        # Check if similarity is above threshold
        if best_student_id and best_sim > FACE_RECOGNITION_THRESHOLD:
            # Check if this student was recently recognized (time window logic)
            if best_student_id in last_attendance_time:
                time_since_last = current_time - last_attendance_time[best_student_id]
                if time_since_last < (ATTENDANCE_TIME_WINDOW * 60):  # Convert minutes to seconds
                    return None

            # Additional verification: check if there's a clear winner
            if (best_sim - second_best_sim) < VERIFICATION_THRESHOLD:
                # For uncertain matches, require a higher threshold
                if best_sim > (FACE_RECOGNITION_THRESHOLD + 0.1):  # Higher threshold for uncertain matches
                    logger.info(f"Recognized student ID {best_student_id} with similarity {best_sim:.4f}")
                    return (best_student_id, best_sim)
                else:
                    return None
            else:
                logger.info(f"Recognized student ID {best_student_id} with similarity {best_sim:.4f}")
                return (best_student_id, best_sim)

        return None
    except Exception as e:
        logger.error(f"Error in face recognition: {e}")
        return None
    finally:
        # Clean up to prevent memory leaks
        if 'faces' in locals():
            del faces
        if 'embedding' in locals():
            del embedding
        if 'processed_frame' in locals():
            del processed_frame
        if 'small_frame' in locals():
            del small_frame
        gc.collect()

class VideoTransformTrack(VideoStreamTrack):
    """Video stream track that transforms frames from another track with optimized performance."""

    def __init__(self, track, connection_id):
        super().__init__()
        self.track = track
        self.connection_id = connection_id
        self.frame_counter = 0
        self.last_recognition_time = 0
        self.last_processed_time = 0
        self.last_frame_time = time.time()
        self.processing_interval = 1.0 / 10.0  # Process at most 10 frames per second
        self.fps_values = []  # Store recent FPS values for smoothing
        self.max_fps_values = 10  # Number of FPS values to keep for averaging
        self.recognized_faces = {}  # Store recently recognized faces with timestamps
        self.last_faces = None  # Cache detected faces
        self.last_detection_time = 0
        self.detection_interval = 0.5  # Detect faces every 0.5 seconds
        self.fps_update_time = time.time()  # Time of last FPS update
        self.data_channel = None  # Data channel for sending messages to client
        self.mode = None  # Mode: 'attendance' or 'registration'
        self.student_id = None  # Student ID for registration mode
        self.captured_faces_count = 0  # Number of captured faces for registration
        self.fps_update_interval = 0.5  # Update FPS display every 0.5 seconds
        self.current_fps_display = 0  # Current FPS value to display
        self.frame_count_since_update = 0  # Frames since last FPS update
        self.last_frame_display_time = time.time()  # Time of last frame display
        logger.info(f"Created video transform track for connection {connection_id}")

    async def recv(self):
        global frame_counter
        frame = None  # Initialize frame to None to handle exceptions properly

        try:
            # Get frame from track
            frame = await self.track.recv()

            # Convert to numpy array
            img = frame.to_ndarray(format="bgr24")

            # Calculate FPS more accurately
            current_time = time.time()
            frame_time = current_time - self.last_frame_time
            self.last_frame_time = current_time

            # Count frames for FPS calculation
            self.frame_count_since_update += 1

            # Update FPS at regular intervals to avoid fluctuations
            if current_time - self.fps_update_time >= self.fps_update_interval:
                elapsed = current_time - self.fps_update_time
                if elapsed > 0 and self.frame_count_since_update > 0:
                    # Calculate actual FPS based on frames processed in the interval
                    actual_fps = self.frame_count_since_update / elapsed
                    # Apply a cap to prevent unrealistic values
                    capped_fps = min(actual_fps, 60.0)  # Cap at 60 FPS for display

                    # Apply smoothing
                    self.fps_values.append(capped_fps)
                    if len(self.fps_values) > self.max_fps_values:
                        self.fps_values.pop(0)

                    # Update the display value
                    self.current_fps_display = sum(self.fps_values) / len(self.fps_values) if self.fps_values else 0

                    # Reset counters
                    self.fps_update_time = current_time
                    self.frame_count_since_update = 0

            # Increment frame counters
            self.frame_counter += 1
            frame_counter += 1

            # Throttle processing to maintain consistent frame rate
            # Skip processing if frames are coming too quickly
            should_process = False
            if current_time - self.last_processed_time >= self.processing_interval:
                if self.frame_counter % FRAME_SKIP == 0:
                    should_process = True
                    self.last_processed_time = current_time

            # Process frame for recognition if it's time
            faces = None
            if should_process:
                # Only detect faces at regular intervals to improve performance
                if current_time - self.last_detection_time > self.detection_interval:
                    self.last_detection_time = current_time

                    try:
                        # Resize image for faster processing
                        h, w = img.shape[:2]
                        small_img = cv2.resize(img, (int(w * DETECTION_SCALE), int(h * DETECTION_SCALE)))

                        # Use InsightFace for face detection on smaller image
                        processed_frame = preprocess_image(small_img)
                        faces = insight_app.get(processed_frame)
                        self.last_faces = faces
                    except Exception as e:
                        logger.error(f"Error detecting faces: {e}")
                else:
                    # Use cached faces
                    faces = self.last_faces

                # Draw faces if available
                if faces:
                    # Draw green boxes around detected faces
                    for face in faces:
                        bbox = face.bbox.astype(int)
                        # Scale bbox back to original image size
                        x1, y1, x2, y2 = [int(coord / DETECTION_SCALE) for coord in bbox]

                        # Draw green rectangle around face
                        cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)

                    # Get the mode and student_id from the connection
                    for conn_id, pc in active_connections.items():
                        if conn_id == self.connection_id:
                            self.mode = getattr(pc, 'mode', 'attendance')
                            self.student_id = getattr(pc, 'student_id', None)
                            break

                    # Handle based on mode
                    if self.mode == 'registration' and self.student_id:
                        # For registration mode, we need to capture multiple face embeddings
                        if len(faces) == 1:  # We need exactly one face for registration
                            # Add to processing queue if not full and not too frequent
                            if not processing_queue.full() and current_time - self.last_recognition_time > 1.0:
                                # Make a copy to avoid race conditions
                                processing_queue.put((img.copy(), self.connection_id))
                                self.last_recognition_time = current_time

                                # Get the face count from the peer connection
                                for conn_id, pc in active_connections.items():
                                    if conn_id == self.connection_id:
                                        if hasattr(pc, 'captured_faces'):
                                            self.captured_faces_count = len(pc.captured_faces)

                                            # Send capture count to client via data channel
                                            try:
                                                if self.data_channel and self.data_channel.readyState == 'open':
                                                    asyncio.ensure_future(self.data_channel.send(json.dumps({
                                                        'type': 'face_detected',
                                                        'count': 1,
                                                        'capture_count': self.captured_faces_count
                                                    })))
                                                    logger.info(f"Sent capture count {self.captured_faces_count}/5 to client")
                                            except Exception as dc_error:
                                                logger.error(f"Error sending capture count: {str(dc_error)[:100]}")
                                        break

                                # Display captured count with progress bar
                                # Draw progress bar background
                                cv2.rectangle(img, (10, 70), (210, 90), (50, 50, 50), -1)

                                # Draw progress bar fill based on captured count
                                progress_width = int((self.captured_faces_count / 5) * 200)
                                cv2.rectangle(img, (10, 70), (10 + progress_width, 90), (0, 255, 0), -1)

                                # Draw text
                                cv2.putText(
                                    img,
                                    f"Captured: {self.captured_faces_count}/5",
                                    (10, 60),
                                    cv2.FONT_HERSHEY_SIMPLEX,
                                    0.6,
                                    (0, 255, 0),
                                    2
                                )

                                # Check if registration is complete - do this check earlier and more aggressively
                                registration_complete_flag = False
                                for conn_id, pc in active_connections.items():
                                    if conn_id == self.connection_id:
                                        # Check both the PC object and the global dictionary
                                        if (hasattr(pc, 'registration_complete') and pc.registration_complete) or \
                                           (self.student_id and self.student_id in registration_complete):
                                            registration_complete_flag = True

                                            # Draw a success message directly on the frame
                                            # This ensures the user sees it even if the data channel fails
                                            cv2.putText(
                                                img,
                                                "Registration Complete!",
                                                (int(img.shape[1]/2) - 150, int(img.shape[0]/2)),
                                                cv2.FONT_HERSHEY_SIMPLEX,
                                                1.0,
                                                (0, 255, 0),  # Green color
                                                3
                                            )

                                            # Also add a message to redirect
                                            cv2.putText(
                                                img,
                                                "Redirecting...",
                                                (int(img.shape[1]/2) - 100, int(img.shape[0]/2) + 40),
                                                cv2.FONT_HERSHEY_SIMPLEX,
                                                0.8,
                                                (0, 255, 0),  # Green color
                                                2
                                            )
                                            # Create success frame
                                            success_img = np.zeros((img.shape[0], img.shape[1], 3), dtype=np.uint8)
                                            success_img[:, :] = (0, 120, 0)  # Green background

                                            # Add success message with larger font and better positioning
                                            # Add white background box for better visibility
                                            text = "Registration Complete!"
                                            font = cv2.FONT_HERSHEY_DUPLEX
                                            font_scale = 1.2
                                            thickness = 2
                                            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]

                                            # Center position
                                            text_x = int((img.shape[1] - text_size[0]) / 2)
                                            text_y = int(img.shape[0] / 2)

                                            # Draw white background box
                                            cv2.rectangle(
                                                success_img,
                                                (text_x - 20, text_y - text_size[1] - 20),
                                                (text_x + text_size[0] + 20, text_y + 20),
                                                (255, 255, 255),
                                                -1
                                            )

                                            # Draw text
                                            cv2.putText(
                                                success_img,
                                                text,
                                                (text_x, text_y),
                                                font,
                                                font_scale,
                                                (0, 100, 0),  # Dark green text
                                                thickness
                                            )

                                            # Add checkmark icon
                                            check_radius = 30
                                            check_center = (text_x - 50, text_y)
                                            cv2.circle(success_img, check_center, check_radius, (255, 255, 255), -1)

                                            # Draw checkmark
                                            check_pts = np.array([
                                                [check_center[0] - 15, check_center[1]],
                                                [check_center[0] - 5, check_center[1] + 10],
                                                [check_center[0] + 15, check_center[1] - 10]
                                            ], np.int32)
                                            cv2.polylines(success_img, [check_pts], False, (0, 100, 0), 5)

                                            # Add "Redirecting..." text
                                            cv2.putText(
                                                success_img,
                                                "Redirecting...",
                                                (text_x, text_y + 40),
                                                cv2.FONT_HERSHEY_SIMPLEX,
                                                0.7,
                                                (255, 255, 255),
                                                1
                                            )

                                            # Replace the image with success message
                                            img = success_img

                                            # Try to send registration complete message via data channel
                                            try:
                                                if self.data_channel and self.data_channel.readyState == 'open':
                                                    # Send only once to avoid multiple messages
                                                    asyncio.ensure_future(self.data_channel.send(json.dumps({
                                                        'type': 'registration_complete',
                                                        'success': True,
                                                        'message': 'Face registration complete',
                                                        'capture_count': 5
                                                    })))

                                                    # Release WebRTC resources after a short delay
                                                    async def release_resources():
                                                        await asyncio.sleep(1.0)
                                                        try:
                                                            # Send a request to release resources
                                                            await asyncio.shield(release_webrtc(self.student_id, 'registration'))
                                                            logger.info(f"Released WebRTC resources for student {self.student_id}")
                                                        except Exception as e:
                                                            logger.error(f"Error releasing resources: {str(e)[:100]}")

                                                    asyncio.ensure_future(release_resources())
                                                    logger.info(f"Sent registration complete message to client for connection {self.connection_id}")
                                                else:
                                                    logger.warning(f"Data channel not available for connection {self.connection_id}")

                                                    # Try to send via the peer connection's data channel as fallback
                                                    for conn_id, pc in active_connections.items():
                                                        if conn_id == self.connection_id and hasattr(pc, 'data_channel'):
                                                            asyncio.ensure_future(pc.data_channel.send(json.dumps({
                                                                'type': 'registration_complete',
                                                                'success': True,
                                                                'message': 'Face registration complete',
                                                                'capture_count': 5
                                                            })))
                                                            logger.info(f"Sent registration complete message via peer connection data channel")
                                                            break
                                            except Exception as dc_error:
                                                logger.error(f"Error sending registration complete message: {str(dc_error)[:100]}")
                                        break
                        else:
                            # Display message to show only one face
                            cv2.putText(
                                img,
                                "Please show only one face",
                                (10, 60),
                                cv2.FONT_HERSHEY_SIMPLEX,
                                0.6,
                                (0, 0, 255),
                                2
                            )
                    else:
                        # For attendance mode, add to processing queue
                        if not processing_queue.full() and current_time - self.last_recognition_time > 1.0:
                            # Make a copy to avoid race conditions
                            processing_queue.put((img.copy(), self.connection_id))
                            self.last_recognition_time = current_time

                            # Log face detection (reduced logging)
                            if self.frame_counter % 30 == 0:  # Log only every 30th processed frame
                                logger.info(f"Detected {len(faces)} faces")

            # Draw FPS with the smoothed value
            cv2.putText(
                img,
                f"FPS: {self.current_fps_display:.1f}",
                (10, 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (0, 255, 0),
                2
            )

            # Add timestamp
            timestamp = time.strftime("%H:%M:%S")
            cv2.putText(
                img,
                timestamp,
                (10, img.shape[0] - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (255, 255, 255),
                1
            )

            # Create new frame from the processed image
            new_frame = frame.from_ndarray(img, format="bgr24")
            new_frame.pts = frame.pts
            new_frame.time_base = frame.time_base

            return new_frame

        except MediaStreamError:
            # This is a normal exception when the track ends
            logger.info(f"Media stream ended for connection {self.connection_id}")
            raise  # Re-raise the exception to properly end the track

        except Exception as e:
            logger.error(f"Error in VideoTransformTrack.recv: {e}")

            # If we have a frame, return it; otherwise re-raise the exception
            if frame is not None:
                return frame
            else:
                raise

async def offer(request):
    """Handle WebRTC offer from client with optimized connection setup."""
    try:
        # Start timing the connection process
        start_time = time.time()

        # Generate a unique connection ID
        connection_id = str(uuid.uuid4())

        # Get offer parameters
        params = await request.json()
        offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])

        # Get mode and student_id from params
        mode = params.get("mode", "attendance")
        student_id = params.get("student_id")

        logger.info(f"Received WebRTC offer for connection {connection_id} in mode {mode}")

        # Create peer connection with standard configuration
        pc = RTCPeerConnection()
        # Store mode and student_id in the peer connection object
        pc.mode = mode
        pc.student_id = student_id

        # Create a data channel for sending messages to the client
        pc.data_channel = pc.createDataChannel('events')

        # Log when data channel is open
        @pc.data_channel.on("open")
        def on_datachannel_open():
            logger.info(f"Data channel opened for connection {connection_id}")
            # If this is a registration connection, store the data channel reference
            if mode == 'registration':
                pc.data_channel_ready = True
                pc.registration_complete_sent = False

                # If registration is already complete, send the message immediately
                if hasattr(pc, 'registration_complete') and pc.registration_complete:
                    # Create a task to send the message
                    asyncio.create_task(send_registration_complete(pc.data_channel, student_id))
                    logger.info(f"Sending registration complete message for student {student_id} on data channel open")

        pcs.add(pc)
        active_connections[connection_id] = pc

        # Handle ICE connection state change
        @pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            logger.info(f"ICE connection state for {connection_id}: {pc.iceConnectionState}")
            if pc.iceConnectionState == "failed" or pc.iceConnectionState == "closed":
                await pc.close()
                pcs.discard(pc)
                if connection_id in active_connections:
                    del active_connections[connection_id]

        # Handle track from client
        @pc.on("track")
        def on_track(track):
            logger.info(f"Track received from client {connection_id}: {track.kind}")
            if track.kind == "video":
                # Create a video transform track with the data channel
                video_track = VideoTransformTrack(relay.subscribe(track), connection_id)
                # Store the data channel in the video track
                video_track.data_channel = pc.data_channel
                # Add transformed track to peer connection
                pc.addTrack(video_track)

                @track.on("ended")
                async def on_ended():
                    logger.info(f"Track ended for connection {connection_id}")

        # Set remote description with simplified timeout
        try:
            # Set remote description
            await pc.setRemoteDescription(offer)

            # Create answer
            answer = await pc.createAnswer()
            await pc.setLocalDescription(answer)
        except Exception as e:
            logger.error(f"Error establishing connection for {connection_id}: {e}")
            await pc.close()
            pcs.discard(pc)
            if connection_id in active_connections:
                del active_connections[connection_id]
            return web.json_response({"error": str(e)}, status=500)

        # Log connection time
        connection_time = time.time() - start_time
        logger.info(f"Created WebRTC connection for {connection_id} in {connection_time:.2f} seconds")

        # Return answer to client
        return web.json_response({
            "sdp": pc.localDescription.sdp,
            "type": pc.localDescription.type,
            "connection_time": connection_time
        })
    except Exception as e:
        logger.error(f"Error handling offer: {e}")
        traceback.print_exc()
        return web.json_response({"error": str(e)}, status=500)

async def on_shutdown(app):
    """Clean up when shutting down."""
    # Close peer connections
    coros = [pc.close() for pc in pcs]
    await asyncio.gather(*coros)
    pcs.clear()

    # Stop background processing
    stop_processing()

    logger.info("Shutdown complete")

async def health(request):
    """Health check endpoint."""
    return web.json_response({"status": "healthy"})

async def stats(request):
    """Return statistics about the WebRTC server."""
    return web.json_response({
        "active_connections": len(active_connections),
        "frame_counter": frame_counter,
        "queue_size": processing_queue.qsize(),
        "processing_active": processing_active
    })

async def release_webrtc(request):
    """Handle WebRTC release request from client."""
    try:
        # Get parameters
        params = await request.json()
        connection_id = params.get("student_id", "unknown")
        mode = params.get("mode", "unknown")

        logger.info(f"Received release request for connection {connection_id} in mode {mode}")

        # Close any active connections for this student/connection
        closed_count = 0
        for conn_id, pc in list(active_connections.items()):
            try:
                await pc.close()
                pcs.discard(pc)
                del active_connections[conn_id]
                closed_count += 1
                logger.info(f"Closed connection {conn_id}")
            except Exception as e:
                logger.error(f"Error closing connection {conn_id}: {e}")

        return web.json_response({
            "success": True,
            "message": f"Released {closed_count} connections",
            "connection_id": connection_id
        })
    except Exception as e:
        logger.error(f"Error handling release request: {e}")
        traceback.print_exc()
        return web.json_response({"error": str(e)}, status=500)

def create_app():
    """Create and configure the web application."""
    # Create application
    app = web.Application()

    # Setup CORS
    cors = setup_cors(app, defaults={
        "*": ResourceOptions(
            allow_credentials=True,
            expose_headers="*",
            allow_headers="*",
            allow_methods=["POST", "GET", "OPTIONS"]
        )
    })

    # Add routes
    app.router.add_post("/offer", offer)
    app.router.add_post("/webrtc/offer", offer)  # Keep only one offer route
    app.router.add_post("/release_webrtc", release_webrtc)
    app.router.add_get("/health", health)
    app.router.add_get("/stats", stats)

    # Configure CORS for all routes
    for route in list(app.router.routes()):
        try:
            cors.add(route)
        except ValueError as e:
            logger.warning(f"Skipping CORS configuration for route {route}: {e}")

    # Add shutdown handler
    app.on_shutdown.append(on_shutdown)

    return app

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='WebRTC server for face recognition')
    parser.add_argument('--port', type=int, default=8080, help='Port to run the server on')
    parser.add_argument('--test-mode', action='store_true', help='Run in test mode')
    args = parser.parse_args()

    # Start background processing
    start_processing()

    # Create and run application
    app = create_app()
    web.run_app(app, host="0.0.0.0", port=args.port)