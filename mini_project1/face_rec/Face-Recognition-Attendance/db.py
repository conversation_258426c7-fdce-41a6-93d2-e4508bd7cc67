from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timezone
import psycopg2
import os
from dotenv import load_dotenv
from sqlalchemy import text
from werkzeug.security import generate_password_hash
import sys
import logging
from flask_login import UserMixin
import numpy as np
from loguru import logger as loguru_logger

# OperationalError is used in exception handling
from sqlalchemy.exc import OperationalError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configure Loguru for enhanced logging
loguru_logger.add(
    "../../logs/db_{time}.log",
    rotation="10 MB",
    retention="1 week",
    level="INFO",
    backtrace=True,
    diagnose=True
)

# Load environment variables
load_dotenv()

# Database configuration from environment
DB_USER = os.getenv('DB_USER')
DB_PASS = os.getenv('DB_PASS')
DB_NAME = os.getenv('DB_NAME')
DB_HOST = os.getenv('DB_HOST')
DB_PORT = os.getenv('DB_PORT')

# Validate required environment variables
required_vars = ['DB_USER', 'DB_PASS', 'DB_NAME', 'DB_HOST', 'DB_PORT']
missing_vars = [var for var in required_vars if not os.getenv(var)]
if missing_vars:
    logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
    sys.exit(1)

# Ensure PostgreSQL connection is available
def verify_postgres_connection():
    try:
        conn = psycopg2.connect(
            dbname='postgres',
            user=DB_USER,
            password=DB_PASS,
            host=DB_HOST,
            port=DB_PORT
        )
        conn.close()
        logger.info("Successfully connected to PostgreSQL server")
        return True
    except Exception as e:
        logger.error(f"Could not connect to PostgreSQL database: {e}")
        logger.error("Please ensure PostgreSQL is running and the connection details are correct.")
        sys.exit(1)

# Verify PostgreSQL connection before proceeding
verify_postgres_connection()

db = SQLAlchemy()

class Student(db.Model):
    __tablename__ = 'students'
    id = db.Column(db.Integer, primary_key=True)
    student_code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(100), nullable=False)
    phone_number = db.Column(db.String(20))
    face_embedding_array = db.Column(db.ARRAY(db.Float), nullable=True)
    created_at = db.Column(db.DateTime(timezone=True), server_default=db.func.now())
    updated_at = db.Column(db.DateTime(timezone=True), onupdate=db.func.now())

    def set_embedding(self, embedding):
        """Set the face embedding for this student."""
        if embedding is not None:
            # Ensure embedding is normalized
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = embedding / norm
            self.face_embedding_array = embedding.tolist()
        else:
            self.face_embedding_array = None

    def get_embedding(self):
        """Get the face embedding for this student."""
        if self.face_embedding_array is not None:
            return np.array(self.face_embedding_array)
        return None

class Attendance(db.Model):
    __tablename__ = 'attendance'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=True)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    # Add a field to track the confidence score of the recognition
    confidence_score = db.Column(db.Float, nullable=True)
    # Add a field to track the device/camera that captured the attendance
    device_id = db.Column(db.String(50), nullable=True)
    # Store student information directly to preserve it when student is deleted
    student_code = db.Column(db.String(20), nullable=True)
    student_name = db.Column(db.String(100), nullable=True)
    student_department = db.Column(db.String(100), nullable=True)
    student = db.relationship('Student', backref=db.backref('records', lazy=True))

    @classmethod
    def mark_attendance(cls, session, student_id, confidence=None, device_id=None, min_interval_minutes=5):
        """Mark attendance with time window logic to prevent duplicates"""
        # Get the current time in UTC
        now = datetime.now(timezone.utc)

        # Check for existing attendance within the time window
        latest = session.query(cls).filter(
            cls.student_id == student_id,
        ).order_by(cls.timestamp.desc()).first()

        # If there's a recent attendance within the time window, don't create a new one
        if latest and (now - latest.timestamp).total_seconds() < (min_interval_minutes * 60):
            loguru_logger.info(f"Skipping duplicate attendance for student {student_id} - last marked {latest.timestamp}")
            return False, latest

        # Get student information to store in attendance record
        from . import Student  # Import here to avoid circular imports
        student = session.query(Student).get(student_id)

        # Create new attendance record with student information
        attendance = cls(
            student_id=student_id,
            timestamp=now,
            confidence_score=confidence,
            device_id=device_id,
            student_code=student.student_code if student else None,
            student_name=student.name if student else None,
            student_department=student.department if student else None
        )
        session.add(attendance)
        session.commit()
        loguru_logger.info(f"Marked attendance for student {student_id} with confidence {confidence}")
        return True, attendance

class FaceImage(db.Model):
    __tablename__ = 'face_images'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    image_data = db.Column(db.LargeBinary, nullable=False)  # Store image as BLOB
    # Store face embedding as PostgreSQL array
    face_embedding_array = db.Column(db.ARRAY(db.Float), nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    student = db.relationship('Student', backref=db.backref('face_images', lazy=True))

    def set_embedding(self, embedding_array):
        """Set face embedding from numpy array"""
        if embedding_array is not None:
            # Store as PostgreSQL array only
            self.face_embedding_array = embedding_array.tolist()
        else:
            self.face_embedding_array = None

    def get_embedding(self):
        """Get face embedding as numpy array"""
        # Get from array column
        if self.face_embedding_array is not None:
            return np.array(self.face_embedding_array)
        return None

class Admin(db.Model, UserMixin):
    __tablename__ = 'admin'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)  # Added email for OTP
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_login = db.Column(db.DateTime, nullable=True)

    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.now(timezone.utc)



class SystemSettings(db.Model):
    __tablename__ = 'system_settings'
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.String(255), nullable=False)
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc),
                          onupdate=lambda: datetime.now(timezone.utc))

    @staticmethod
    def get_setting(key, default=None):
        """Get a system setting by key with an optional default value."""
        setting = SystemSettings.query.filter_by(key=key).first()
        return setting.value if setting else default

    @staticmethod
    def set_setting(key, value):
        """Set a system setting by key and value."""
        setting = SystemSettings.query.filter_by(key=key).first()
        if setting:
            setting.value = str(value)
        else:
            setting = SystemSettings(key=key, value=str(value))
            db.session.add(setting)
        db.session.commit()

def ensure_database_exists():
    """Ensure the PostgreSQL database exists"""
    try:
        # Connect to PostgreSQL server
        conn = psycopg2.connect(
            dbname='postgres',
            user=DB_USER,
            password=DB_PASS,
            host=DB_HOST,
            port=DB_PORT
        )
        conn.autocommit = True
        cur = conn.cursor()

        # Check if database exists
        cur.execute(f"SELECT 1 FROM pg_database WHERE datname = '{DB_NAME}'")
        exists = cur.fetchone()

        if not exists:
            cur.execute(f"CREATE DATABASE {DB_NAME}")
            logger.info(f"Created database '{DB_NAME}'")
        else:
            logger.info(f"Database '{DB_NAME}' already exists")

        cur.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error ensuring database exists: {e}")
        raise

def create_tables_if_missing():
    """Create database tables if they don't exist and handle migrations"""
    try:
        engine = db.get_engine()
        inspector = db.inspect(engine)

        # Create tables if they don't exist
        db.create_all()

        # Handle migrations
        if 'students' in inspector.get_table_names():
            with engine.connect() as conn:
                try:
                    # Add new columns if they don't exist
                    migrations = [
                        "ALTER TABLE students ADD COLUMN IF NOT EXISTS department VARCHAR(100)",
                        "ALTER TABLE students ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()",
                        "ALTER TABLE students ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()",
                        "ALTER TABLE students ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20)",
                        # Add the ARRAY column for face embeddings
                        "ALTER TABLE students ADD COLUMN IF NOT EXISTS face_embedding_array FLOAT[]",
                        # Add JSON column for backward compatibility
                        "ALTER TABLE students ADD COLUMN IF NOT EXISTS face_embedding JSONB",
                        # Drop old columns if they exist
                        "ALTER TABLE students DROP COLUMN IF EXISTS face_embedding_json",
                        "ALTER TABLE face_images ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()",
                        # Add face_embedding_array FLOAT[] column to face_images
                        "ALTER TABLE face_images ADD COLUMN IF NOT EXISTS face_embedding_array FLOAT[]",
                        # Drop old column if it exists
                        "ALTER TABLE face_images DROP COLUMN IF EXISTS face_embedding"
                    ]
                    for migration in migrations:
                        conn.execute(text(migration))

                    # Copy face_embedding_array to face_embedding for compatibility
                    migrate_embeddings = """
                    UPDATE students
                    SET face_embedding = to_jsonb(face_embedding_array)
                    WHERE face_embedding_array IS NOT NULL AND face_embedding IS NULL
                    """
                    try:
                        conn.execute(text(migrate_embeddings))
                        loguru_logger.info("Migrated face embeddings from JSON to ARRAY format")
                    except Exception as e:
                        loguru_logger.warning(f"Error migrating embeddings (can be ignored if no embeddings exist): {e}")

                    conn.commit()
                    loguru_logger.info("Successfully applied student table migrations")
                except Exception as e:
                    loguru_logger.warning(f"Column addition error (can be ignored if columns exist): {e}")

        if 'attendance' in inspector.get_table_names():
            with engine.connect() as conn:
                try:
                    migrations = [
                        "ALTER TABLE attendance ADD COLUMN IF NOT EXISTS confidence_score FLOAT",
                        "ALTER TABLE attendance ADD COLUMN IF NOT EXISTS device_id VARCHAR(50)",
                        # Update timestamp to be timezone aware
                        "ALTER TABLE attendance ALTER COLUMN timestamp TYPE TIMESTAMP WITH TIME ZONE",
                        # Allow NULL values in student_id for deleted students
                        "ALTER TABLE attendance ALTER COLUMN student_id DROP NOT NULL",
                        # Add columns to store student information directly
                        "ALTER TABLE attendance ADD COLUMN IF NOT EXISTS student_code VARCHAR(20)",
                        "ALTER TABLE attendance ADD COLUMN IF NOT EXISTS student_name VARCHAR(100)",
                        "ALTER TABLE attendance ADD COLUMN IF NOT EXISTS student_department VARCHAR(100)"
                    ]
                    for migration in migrations:
                        conn.execute(text(migration))

                    # Populate existing attendance records with student information
                    populate_student_info = """
                    UPDATE attendance
                    SET student_code = students.student_code,
                        student_name = students.name,
                        student_department = students.department
                    FROM students
                    WHERE attendance.student_id = students.id
                    AND (attendance.student_code IS NULL OR attendance.student_name IS NULL OR attendance.student_department IS NULL)
                    """
                    try:
                        conn.execute(text(populate_student_info))
                        loguru_logger.info("Populated existing attendance records with student information")
                    except Exception as e:
                        loguru_logger.warning(f"Error populating student info (can be ignored if no existing records): {e}")

                    conn.commit()
                    loguru_logger.info("Successfully applied attendance table migrations")
                except Exception as e:
                    loguru_logger.warning(f"Attendance column migration error: {e}")

        if 'admin' in inspector.get_table_names():
            with engine.connect() as conn:
                try:
                    migrations = [
                        "ALTER TABLE admin ALTER COLUMN password_hash TYPE VARCHAR(256)",
                        "ALTER TABLE admin ADD COLUMN IF NOT EXISTS email VARCHAR(120)",
                        "ALTER TABLE admin ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()",
                        "ALTER TABLE admin ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE"
                    ]
                    for migration in migrations:
                        conn.execute(text(migration))
                    conn.commit()
                    loguru_logger.info("Successfully applied admin table migrations")
                except Exception as e:
                    loguru_logger.warning(f"Admin column migration error: {e}")



        # Create system_settings table if it doesn't exist
        if 'system_settings' not in inspector.get_table_names():
            try:
                # Create the table
                SystemSettings.__table__.create(db.engine)
                loguru_logger.info("Created system_settings table")

                # Add default sleep timer setting (5 minutes)
                default_sleep_timer = SystemSettings(key='sleep_timer_seconds', value='300')
                db.session.add(default_sleep_timer)
                db.session.commit()
                loguru_logger.info("Added default sleep timer setting")
            except Exception as e:
                loguru_logger.warning(f"Error creating system_settings table: {e}")
        else:
            # Check if sleep_timer_seconds setting exists
            try:
                sleep_timer = SystemSettings.query.filter_by(key='sleep_timer_seconds').first()
                if not sleep_timer:
                    # Add default sleep timer setting (5 minutes)
                    default_sleep_timer = SystemSettings(key='sleep_timer_seconds', value='300')
                    db.session.add(default_sleep_timer)
                    db.session.commit()
                    loguru_logger.info("Added default sleep timer setting")
            except Exception as e:
                loguru_logger.warning(f"Error checking/adding sleep timer setting: {e}")

        # Ensure at least one admin exists
        if not Admin.query.first():
            admin_username = os.getenv('ADMIN_USERNAME', 'admin')
            admin_password = os.getenv('ADMIN_PASSWORD', 'admin123')
            admin_email = os.getenv('ADMIN_EMAIL', '<EMAIL>')

            default_admin = Admin(
                username=admin_username,
                password_hash=generate_password_hash(admin_password),
                email=admin_email,
                created_at=datetime.now(timezone.utc)
            )
            db.session.add(default_admin)
            db.session.commit()
            loguru_logger.info("Created default admin user")

        # Add database health check function
        with engine.connect() as conn:
            try:
                conn.execute(text("""
                CREATE OR REPLACE FUNCTION check_db_health() RETURNS boolean AS $$
                BEGIN
                    RETURN true;
                END;
                $$ LANGUAGE plpgsql;
                """))
                conn.commit()
                loguru_logger.info("Created database health check function")
            except Exception as e:
                loguru_logger.warning(f"Error creating health check function: {e}")

        loguru_logger.info("Database tables and columns verified successfully")
    except Exception as e:
        loguru_logger.error(f"Database setup error: {e}")
        raise