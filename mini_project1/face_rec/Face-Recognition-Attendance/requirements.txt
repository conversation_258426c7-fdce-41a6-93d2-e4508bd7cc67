# Core Dependencies
Flask>=2.3.3
Flask-SQLAlchemy>=3.1.1
Flask-Login>=0.6.2
Flask-Migrate>=4.0.5
Flask-WTF>=1.2.1
python-dotenv>=1.0.0
Werkzeug>=2.3.7
Jinja2>=3.1.2
click>=8.1.3
itsdangerous>=2.2.0
Flask-SocketIO>=5.3.6  # Required for WebSocket communication
Flask-Talisman>=1.1.0  # Required for security headers
Flask-Limiter>=3.5.0  # Required for rate limiting

# Face Recognition
opencv-python>=********
numpy>=1.26.2
Pillow>=10.1.0
scikit-learn>=1.3.2  # For face similarity calculations
scipy>=1.11.3  # Required for scikit-learn
insightface>=0.7.3  # Required for face recognition

# WebRTC and Video Streaming
aiortc>=1.5.0
aiohttp>=3.8.6
aiohttp_cors>=0.7.0  # Required for CORS in aiohttp
websockets>=12.0
requests>=2.31.0  # Required for API calls

# Database
psycopg2-binary>=2.9.9
SQLAlchemy>=2.0.23
pandas>=2.1.0  # Required for data export

# Security
cryptography>=41.0.5
PyJWT>=2.8.0
bcrypt>=4.0.1

# Monitoring and Logging
psutil>=5.9.6
loguru>=0.7.2  # Enhanced logging

# Production
gunicorn>=21.2.0
eventlet>=0.35.2
xlsxwriter>=3.1.0  # Required for Excel export

# Additional Dependencies
onnxruntime>=1.15.0  # Required for insightface with GPU support on Mac M1/M2 via CoreML

# Optional Dependencies (uncomment if needed)
# redis>=5.0.1  # Only if using Redis for caching
# pgvector>=0.2.3  # Only if using vector similarity search in PostgreSQL
# Flask-Caching>=2.1.0  # Only if using caching