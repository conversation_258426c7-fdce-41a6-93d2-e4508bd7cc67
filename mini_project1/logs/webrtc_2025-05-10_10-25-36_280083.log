2025-05-10 10:25:36.284 | INFO     | __main__:process_queue:110 - Starting background processing thread
2025-05-10 10:25:36.284 | INFO     | __main__:start_processing:348 - Started background processing thread
2025-05-10 10:26:11.420 | INFO     | __main__:release_webrtc:985 - Received release request for connection 208 in mode registration
2025-05-10 10:26:13.297 | INFO     | __main__:release_webrtc:985 - Received release request for connection 208 in mode registration
2025-05-10 10:26:13.301 | INFO     | __main__:offer:866 - Received WebRTC offer for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88 in mode registration
2025-05-10 10:26:13.313 | INFO     | __main__:on_track:908 - Track received from client 2bd5cf3a-0ede-4796-bced-877c6d56da88: video
2025-05-10 10:26:13.313 | INFO     | __main__:__init__:540 - Created video transform track for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:18.319 | INFO     | __main__:offer:939 - Created WebRTC connection for 2bd5cf3a-0ede-4796-bced-877c6d56da88 in 5.02 seconds
2025-05-10 10:26:18.322 | INFO     | __main__:on_iceconnectionstatechange:898 - ICE connection state for 2bd5cf3a-0ede-4796-bced-877c6d56da88: checking
2025-05-10 10:26:18.357 | INFO     | __main__:on_iceconnectionstatechange:898 - ICE connection state for 2bd5cf3a-0ede-4796-bced-877c6d56da88: new
2025-05-10 10:26:19.425 | INFO     | __main__:process_queue:163 - Captured face 1/5 for student 208
2025-05-10 10:26:20.010 | INFO     | __main__:process_queue:163 - Captured face 2/5 for student 208
2025-05-10 10:26:20.741 | INFO     | __main__:process_queue:163 - Captured face 3/5 for student 208
2025-05-10 10:26:21.874 | INFO     | __main__:process_queue:163 - Captured face 4/5 for student 208
2025-05-10 10:26:22.968 | INFO     | __main__:process_queue:163 - Captured face 5/5 for student 208
2025-05-10 10:26:22.969 | INFO     | __main__:process_queue:170 - Immediately set registration_complete flag for student 208
2025-05-10 10:26:22.972 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:22.972 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:22.972 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:22.972 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:23.964 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:23.964 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:24.192 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:24.192 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:24.192 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:24.192 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:25.101 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:25.102 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:25.324 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:25.325 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:25.325 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:25.325 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:26.239 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:26.239 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:26.476 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:26.476 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:26.476 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:26.476 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:27.371 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:27.371 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:27.604 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:27.604 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:27.604 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:27.604 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:28.511 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:28.512 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:28.749 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:28.749 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:28.749 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:28.749 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:29.636 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:29.636 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:29.875 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:29.876 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:29.876 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:29.876 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:30.813 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:30.814 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:31.051 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:31.051 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:31.051 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:31.051 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:31.944 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:31.944 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:32.179 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:32.179 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:32.179 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:32.179 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:33.072 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:33.072 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:33.300 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:33.300 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:33.300 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:33.300 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:34.185 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:34.185 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:34.420 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:34.420 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:34.420 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:34.420 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:35.326 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:35.326 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:35.552 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:35.553 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:35.553 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:35.553 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:36.462 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:36.462 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:36.703 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:36.703 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:36.703 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:36.703 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:36.703 | INFO     | __main__:process_queue:334 - Performance: 17 frames, 0 recognized (0.0%), FPS: 0.28
2025-05-10 10:26:37.602 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:37.602 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:37.839 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:37.839 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:37.839 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:37.839 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:38.669 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:38.669 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:38.912 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:38.912 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:38.913 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:38.913 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:39.805 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:39.806 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:40.036 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:40.036 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:40.036 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:40.036 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:40.936 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:40.936 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:41.177 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:41.177 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:41.177 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:41.178 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:42.068 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:42.069 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:42.302 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:42.303 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:42.303 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:42.303 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:26:43.132 | WARNING  | __main__:recv:767 - Data channel not available for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:43.132 | ERROR    | __main__:recv:781 - Error sending registration complete message: 
2025-05-10 10:26:43.343 | INFO     | __main__:on_ended:919 - Track ended for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:43.344 | INFO     | __main__:recv:837 - Media stream ended for connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:43.348 | INFO     | __main__:release_webrtc:985 - Received release request for connection 208 in mode registration
2025-05-10 10:26:43.349 | INFO     | __main__:on_iceconnectionstatechange:898 - ICE connection state for 2bd5cf3a-0ede-4796-bced-877c6d56da88: closed
2025-05-10 10:26:43.351 | INFO     | __main__:release_webrtc:995 - Closed connection 2bd5cf3a-0ede-4796-bced-877c6d56da88
2025-05-10 10:26:43.389 | WARNING  | __main__:process_queue:233 - Authentication failed (403) for student 208, retry 1/3
2025-05-10 10:26:43.390 | INFO     | __main__:process_queue:239 - Using fallback method for student 208
2025-05-10 10:26:43.390 | WARNING  | __main__:process_queue:271 - Data channel not ready for student 208
2025-05-10 10:26:43.390 | ERROR    | __main__:process_queue:284 - Error creating task to send registration complete: no running event loop
2025-05-10 10:28:08.786 | INFO     | __main__:stop_processing:357 - Stopping background processing thread...
2025-05-10 10:28:09.099 | INFO     | __main__:stop_processing:366 - Background processing thread stopped successfully
2025-05-10 10:28:09.100 | INFO     | __main__:on_shutdown:962 - Shutdown complete
