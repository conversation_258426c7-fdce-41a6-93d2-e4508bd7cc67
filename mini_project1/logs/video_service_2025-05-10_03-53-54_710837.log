2025-05-10 03:53:54.769 | INFO     | db:create_tables_if_missing:271 - Migrated face embeddings from JSON to ARRAY format
2025-05-10 03:53:54.770 | INFO     | db:create_tables_if_missing:276 - Successfully applied student table migrations
2025-05-10 03:53:54.771 | INFO     | db:create_tables_if_missing:292 - Successfully applied attendance table migrations
2025-05-10 03:53:54.772 | INFO     | db:create_tables_if_missing:308 - Successfully applied admin table migrations
2025-05-10 03:53:54.774 | INFO     | db:create_tables_if_missing:325 - Successfully applied trainer_files table migrations
2025-05-10 03:53:54.788 | INFO     | db:create_tables_if_missing:356 - Created database health check function
2025-05-10 03:53:54.788 | INFO     | db:create_tables_if_missing:360 - Database tables and columns verified successfully
2025-05-10 03:54:16.883 | INFO     | video_service:initialize_camera:133 - Camera initialized successfully with resolution (640, 480)
2025-05-10 03:54:16.884 | INFO     | video_service:_capture_loop:233 - Frame capture loop started
2025-05-10 03:54:16.884 | INFO     | video_service:_processing_loop:282 - Frame processing loop started
2025-05-10 03:54:16.884 | INFO     | video_service:_streaming_loop:314 - Streaming loop started
2025-05-10 03:54:16.884 | INFO     | video_service:start_recognition:180 - Started recognition with separate capture and processing threads
2025-05-10 03:54:17.567 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:18.176 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:18.725 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:19.313 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:19.897 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:20.462 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:21.058 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:21.625 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:22.178 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:22.753 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:23.374 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:23.945 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:24.108 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:24.686 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:25.282 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:25.893 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:26.478 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:26.971 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:27.611 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:28.210 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:28.814 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:29.326 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:29.973 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:30.521 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:31.138 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:31.710 | ERROR    | video_service:process_frame:665 - Error processing frame: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-10 03:54:32.338 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:32.915 | ERROR    | video_service:_processing_loop:307 - Error in processing loop: <ContextVar name='flask.app_ctx' at 0x10c64aa70>
2025-05-10 03:54:32.951 | INFO     | video_service:_streaming_loop:337 - Streaming loop stopped
2025-05-10 03:54:32.999 | INFO     | video_service:_capture_loop:278 - Frame capture loop stopped
2025-05-10 03:54:33.018 | INFO     | video_service:_processing_loop:310 - Frame processing loop stopped
