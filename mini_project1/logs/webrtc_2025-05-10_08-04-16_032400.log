2025-05-10 08:04:16.037 | INFO     | __main__:process_queue:77 - Starting background processing thread
2025-05-10 08:04:16.037 | INFO     | __main__:start_processing:174 - Started background processing thread
2025-05-10 08:06:32.248 | INFO     | __main__:release_webrtc:583 - Received release request for connection unknown in mode attendance
2025-05-10 08:06:38.047 | INFO     | __main__:release_webrtc:583 - Received release request for connection unknown in mode attendance
2025-05-10 08:06:38.068 | INFO     | __main__:on_track:522 - Track received from client baee50e9-0683-4f95-9cab-abd1ea68e357: video
2025-05-10 08:06:38.068 | INFO     | __main__:__init__:355 - Created video transform track for connection baee50e9-0683-4f95-9cab-abd1ea68e357
2025-05-10 08:06:43.073 | INFO     | __main__:offer:538 - Created WebRTC connection for baee50e9-0683-4f95-9cab-abd1ea68e357
2025-05-10 08:06:43.075 | INFO     | __main__:on_iceconnectionstatechange:512 - ICE connection state for baee50e9-0683-4f95-9cab-abd1ea68e357: checking
2025-05-10 08:06:43.108 | INFO     | __main__:on_iceconnectionstatechange:512 - ICE connection state for baee50e9-0683-4f95-9cab-abd1ea68e357: completed
2025-05-10 08:06:43.556 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:43.994 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:44.025 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:44.432 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:44.483 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:44.889 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:44.926 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:45.337 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:45.364 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:45.792 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:45.800 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:46.226 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:46.255 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:46.664 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:46.701 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:47.122 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:47.154 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:47.578 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:47.608 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:48.052 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:48.096 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:48.536 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:48.587 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:49.016 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:49.035 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:49.461 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:49.487 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:49.913 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:49.933 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:50.612 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:50.617 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:51.134 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:51.147 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:51.838 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:51.870 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:52.322 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:52.325 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:52.814 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:52.843 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:53.289 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:53.324 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:53.821 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:53.826 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:54.289 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:54.321 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:54.762 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:54.775 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:55.248 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:55.282 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:55.744 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:55.780 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:56.303 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:56.333 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:56.804 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:56.834 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:57.278 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:57.312 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:57.836 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:57.869 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:58.329 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:58.361 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:58.815 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:58.885 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:59.329 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:06:59.462 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:06:59.827 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:00.077 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:00.380 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:00.603 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:00.939 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:01.171 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:01.486 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:01.781 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:02.074 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:02.348 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:02.621 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:03.108 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:03.467 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:04.483 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:04.950 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:05.438 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:05.529 | ERROR    | __main__:recv:488 - Error in VideoTransformTrack.recv: 'RTCPeerConnection' object has no attribute '_sctp'
2025-05-10 08:07:05.530 | INFO     | __main__:on_ended:529 - Track ended for connection baee50e9-0683-4f95-9cab-abd1ea68e357
2025-05-10 08:07:05.538 | INFO     | __main__:on_iceconnectionstatechange:512 - ICE connection state for baee50e9-0683-4f95-9cab-abd1ea68e357: closed
2025-05-10 08:07:05.540 | INFO     | __main__:stop_processing:183 - Stopping background processing thread...
2025-05-10 08:07:06.292 | ERROR    | __main__:process_frame_for_recognition:250 - Failed to get students: 403 - 
2025-05-10 08:07:06.544 | INFO     | __main__:stop_processing:192 - Background processing thread stopped successfully
2025-05-10 08:07:06.544 | INFO     | __main__:on_shutdown:560 - Shutdown complete
