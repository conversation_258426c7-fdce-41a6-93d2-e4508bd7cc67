2025-05-10 10:17:01.329 | INFO     | __main__:process_queue:110 - Starting background processing thread
2025-05-10 10:17:01.330 | INFO     | __main__:start_processing:287 - Started background processing thread
2025-05-10 10:17:52.473 | INFO     | __main__:release_webrtc:917 - Received release request for connection 206 in mode registration
2025-05-10 10:17:54.344 | INFO     | __main__:release_webrtc:917 - Received release request for connection 206 in mode registration
2025-05-10 10:17:54.349 | INFO     | __main__:offer:805 - Received WebRTC offer for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11 in mode registration
2025-05-10 10:17:54.362 | INFO     | __main__:on_track:840 - Track received from client 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11: video
2025-05-10 10:17:54.362 | INFO     | __main__:__init__:479 - Created video transform track for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:17:59.368 | INFO     | __main__:offer:871 - Created WebRTC connection for 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11 in 5.02 seconds
2025-05-10 10:17:59.371 | INFO     | __main__:on_iceconnectionstatechange:830 - ICE connection state for 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11: checking
2025-05-10 10:17:59.408 | INFO     | __main__:on_iceconnectionstatechange:830 - ICE connection state for 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11: new
2025-05-10 10:18:00.066 | INFO     | __main__:process_queue:163 - Captured face 1/5 for student 206
2025-05-10 10:18:01.007 | INFO     | __main__:process_queue:163 - Captured face 2/5 for student 206
2025-05-10 10:18:02.130 | INFO     | __main__:process_queue:163 - Captured face 3/5 for student 206
2025-05-10 10:18:02.130 | INFO     | __main__:process_queue:273 - Performance: 3 frames, 0 recognized (0.0%), FPS: 0.05
2025-05-10 10:18:03.250 | INFO     | __main__:process_queue:163 - Captured face 4/5 for student 206
2025-05-10 10:18:04.383 | INFO     | __main__:process_queue:163 - Captured face 5/5 for student 206
2025-05-10 10:18:04.384 | INFO     | __main__:process_queue:170 - Immediately set registration_complete flag for student 206
2025-05-10 10:18:04.390 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:05.115 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:05.115 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:05.536 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:06.240 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:06.240 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:06.613 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:07.376 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:07.377 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:07.748 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:08.378 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:08.378 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:08.626 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:09.503 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:09.503 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:09.753 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:10.628 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:10.629 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:10.875 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:11.848 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:11.848 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:12.087 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:12.937 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:12.937 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:13.167 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:14.071 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:14.071 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:14.326 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:15.137 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:15.137 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:15.378 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:16.184 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:16.184 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:16.417 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:17.320 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:17.320 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:17.564 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:18.462 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:18.462 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:18.720 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:19.534 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:19.535 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:19.771 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:20.585 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:20.585 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:20.979 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:21.639 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:21.640 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:21.905 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:22.777 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:22.777 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:23.033 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:23.921 | WARNING  | __main__:recv:706 - Data channel not available for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:23.922 | ERROR    | __main__:recv:720 - Error sending registration complete message: 
2025-05-10 10:18:24.163 | ERROR    | __main__:process_queue:223 - Failed to register face: 403
2025-05-10 10:18:25.062 | INFO     | __main__:release_webrtc:917 - Received release request for connection 206 in mode registration
2025-05-10 10:18:25.064 | INFO     | __main__:on_ended:851 - Track ended for connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:18:25.065 | INFO     | __main__:on_iceconnectionstatechange:830 - ICE connection state for 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11: closed
2025-05-10 10:18:25.066 | INFO     | __main__:release_webrtc:927 - Closed connection 8a6cdd4a-a3c6-4cfd-9242-3e50af7a3d11
2025-05-10 10:20:33.081 | INFO     | __main__:stop_processing:296 - Stopping background processing thread...
2025-05-10 10:20:33.278 | INFO     | __main__:stop_processing:305 - Background processing thread stopped successfully
2025-05-10 10:20:33.278 | INFO     | __main__:on_shutdown:894 - Shutdown complete
