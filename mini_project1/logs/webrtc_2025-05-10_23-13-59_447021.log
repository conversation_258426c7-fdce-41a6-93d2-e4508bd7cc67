2025-05-10 23:13:59.451 | INFO     | __main__:process_queue:110 - Starting background processing thread
2025-05-10 23:13:59.452 | INFO     | __main__:start_processing:349 - Started background processing thread
2025-05-10 23:14:37.783 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 213 in mode registration
2025-05-10 23:14:40.172 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 213 in mode registration
2025-05-10 23:14:40.176 | INFO     | __main__:offer:900 - Received WebRTC offer for connection 799a5706-9122-4dba-83fd-b23580f9a184 in mode registration
2025-05-10 23:14:40.196 | INFO     | __main__:on_track:942 - Track received from client 799a5706-9122-4dba-83fd-b23580f9a184: video
2025-05-10 23:14:40.196 | INFO     | __main__:__init__:541 - Created video transform track for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:40.415 | INFO     | __main__:offer:973 - Created WebRTC connection for 799a5706-9122-4dba-83fd-b23580f9a184 in 0.24 seconds
2025-05-10 23:14:40.417 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 799a5706-9122-4dba-83fd-b23580f9a184: checking
2025-05-10 23:14:40.983 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 799a5706-9122-4dba-83fd-b23580f9a184: new
2025-05-10 23:14:41.970 | INFO     | __main__:process_queue:163 - Captured face 1/5 for student 213
2025-05-10 23:14:42.722 | INFO     | __main__:process_queue:163 - Captured face 2/5 for student 213
2025-05-10 23:14:43.895 | INFO     | __main__:process_queue:163 - Captured face 3/5 for student 213
2025-05-10 23:14:44.973 | INFO     | __main__:process_queue:163 - Captured face 4/5 for student 213
2025-05-10 23:14:46.102 | INFO     | __main__:process_queue:163 - Captured face 5/5 for student 213
2025-05-10 23:14:46.102 | INFO     | __main__:process_queue:170 - Immediately set registration_complete flag for student 213
2025-05-10 23:14:46.102 | ERROR    | __main__:process_queue:197 - Error processing registration completion: 'numpy.ndarray' object has no attribute 'embedding'
2025-05-10 23:14:46.107 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:46.107 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:46.107 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:46.107 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:47.009 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:47.009 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:47.252 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:47.252 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:47.252 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:47.252 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:48.173 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:48.173 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:48.416 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:48.416 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:48.417 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:48.417 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:49.284 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:49.284 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:49.518 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:49.519 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:49.519 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:49.519 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:50.413 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:50.414 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:50.647 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:50.647 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:50.647 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:50.647 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:51.522 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:51.522 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:51.747 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:51.747 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:51.748 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:51.748 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:52.304 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:52.305 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:52.730 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:52.730 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:52.730 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:52.730 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:53.361 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:53.361 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:53.740 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:53.740 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:53.741 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:53.741 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:54.427 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:54.427 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:54.844 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:54.844 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:54.844 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:54.845 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:55.435 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:55.435 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:55.667 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:55.667 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:55.667 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:55.668 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:56.556 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:56.556 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:56.865 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:56.865 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:56.865 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:56.865 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:57.719 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:57.719 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:57.979 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:57.979 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:57.979 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:57.979 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:14:58.830 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:14:58.830 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:14:59.053 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:14:59.053 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:14:59.053 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:14:59.053 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:15:00.037 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:15:00.037 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:15:00.300 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:15:00.300 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:15:00.300 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:15:00.300 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:15:00.300 | INFO     | __main__:process_queue:335 - Performance: 18 frames, 0 recognized (0.0%), FPS: 0.30
2025-05-10 23:15:01.109 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:15:01.110 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:15:01.367 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:15:01.367 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:15:01.367 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:15:01.367 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:15:02.200 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:15:02.200 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:15:02.458 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:15:02.459 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:15:02.459 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:15:02.459 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:15:03.316 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:15:03.316 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:15:03.583 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:15:03.583 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:15:03.583 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:15:03.583 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:15:04.375 | WARNING  | __main__:recv:801 - Data channel not available for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:15:04.375 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 23:15:04.606 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 213, retry 1/3
2025-05-10 23:15:04.607 | INFO     | __main__:process_queue:240 - Using fallback method for student 213
2025-05-10 23:15:04.607 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 213
2025-05-10 23:15:04.607 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 23:15:05.443 | INFO     | __main__:on_ended:953 - Track ended for connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:15:05.443 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 213 in mode registration
2025-05-10 23:15:05.444 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 799a5706-9122-4dba-83fd-b23580f9a184: closed
2025-05-10 23:15:05.444 | INFO     | __main__:release_webrtc:1029 - Closed connection 799a5706-9122-4dba-83fd-b23580f9a184
2025-05-10 23:15:05.447 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 213 in mode registration
2025-05-10 23:17:53.907 | INFO     | __main__:release_webrtc:1019 - Received release request for connection unknown in mode attendance
2025-05-10 23:17:54.851 | INFO     | __main__:release_webrtc:1019 - Received release request for connection unknown in mode attendance
2025-05-10 23:17:54.855 | INFO     | __main__:offer:900 - Received WebRTC offer for connection 753cda66-e51b-44e5-94d6-cfd5cdcb3066 in mode attendance
2025-05-10 23:17:54.857 | INFO     | __main__:on_track:942 - Track received from client 753cda66-e51b-44e5-94d6-cfd5cdcb3066: video
2025-05-10 23:17:54.857 | INFO     | __main__:__init__:541 - Created video transform track for connection 753cda66-e51b-44e5-94d6-cfd5cdcb3066
2025-05-10 23:17:54.902 | INFO     | __main__:offer:973 - Created WebRTC connection for 753cda66-e51b-44e5-94d6-cfd5cdcb3066 in 0.05 seconds
2025-05-10 23:17:54.903 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 753cda66-e51b-44e5-94d6-cfd5cdcb3066: checking
2025-05-10 23:17:54.927 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 753cda66-e51b-44e5-94d6-cfd5cdcb3066: new
2025-05-10 23:17:56.028 | INFO     | __main__:process_queue:335 - Performance: 23 frames, 0 recognized (0.0%), FPS: 0.10
2025-05-10 23:18:03.014 | ERROR    | __main__:process_queue:324 - Error marking attendance via API: cannot access local variable 'confidence' where it is not associated with a value
2025-05-10 23:18:06.154 | INFO     | __main__:recv:837 - Detected 1 faces
2025-05-10 23:18:08.467 | INFO     | __main__:release_webrtc:1019 - Received release request for connection unknown in mode attendance
2025-05-10 23:18:08.467 | INFO     | __main__:on_ended:953 - Track ended for connection 753cda66-e51b-44e5-94d6-cfd5cdcb3066
2025-05-10 23:18:08.468 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 753cda66-e51b-44e5-94d6-cfd5cdcb3066: closed
2025-05-10 23:18:08.468 | INFO     | __main__:release_webrtc:1029 - Closed connection 753cda66-e51b-44e5-94d6-cfd5cdcb3066
2025-05-10 23:19:23.317 | INFO     | __main__:stop_processing:358 - Stopping background processing thread...
2025-05-10 23:19:23.655 | INFO     | __main__:stop_processing:367 - Background processing thread stopped successfully
2025-05-10 23:19:23.655 | INFO     | __main__:on_shutdown:996 - Shutdown complete
