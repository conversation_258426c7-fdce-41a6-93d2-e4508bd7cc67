2025-05-10 06:45:29.972 | INFO     | __main__:main:255 - Starting WebRTC tests...
2025-05-10 06:45:29.972 | INFO     | __main__:test_webrtc_server:40 - Testing WebRTC server...
2025-05-10 06:45:30.088 | ERROR    | __main__:test_webrtc_server:90 - Error testing WebRTC server: No module named 'aiohttp_cors'
2025-05-10 06:45:30.088 | INFO     | __main__:main:259 - WebRTC server test failed
2025-05-10 06:45:30.088 | INFO     | __main__:test_webrtc_client:132 - Testing WebRTC client...
2025-05-10 06:45:30.088 | INFO     | __main__:test_webrtc_client:237 - Created test HTML file at templates/webrtc_test.html
2025-05-10 06:45:30.089 | INFO     | __main__:test_webrtc_client:243 - Started test client app at http://localhost:5002
2025-05-10 06:45:30.089 | INFO     | __main__:test_webrtc_client:244 - Open this URL in your browser to test the WebRTC client
2025-05-10 06:45:30.089 | INFO     | __main__:test_webrtc_client:245 - WebRTC client test setup complete
2025-05-10 06:45:30.090 | INFO     | __main__:main:263 - WebRTC client test setup complete
2025-05-10 06:45:30.090 | INFO     | __main__:main:266 - Open http://localhost:5002 in your browser to test the WebRTC client
2025-05-10 06:46:38.583 | INFO     | __main__:main:273 - Test script terminated by user
2025-05-10 06:46:38.583 | INFO     | __main__:main:275 - WebRTC tests completed
2025-05-10 06:46:44.362 | ERROR    | __main__:webrtc_offer:114 - Error handling WebRTC offer: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /webrtc/offer (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x162337d10>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-10 06:46:44.374 | ERROR    | __main__:release_webrtc:125 - Error releasing WebRTC resources: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /webrtc/release (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x16234b9d0>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-10 06:46:57.322 | ERROR    | __main__:webrtc_offer:114 - Error handling WebRTC offer: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /webrtc/offer (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x162357650>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-10 06:46:57.333 | ERROR    | __main__:release_webrtc:125 - Error releasing WebRTC resources: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /webrtc/release (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x162355e10>: Failed to establish a new connection: [Errno 61] Connection refused'))
