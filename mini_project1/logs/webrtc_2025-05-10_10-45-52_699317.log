2025-05-10 10:45:52.704 | INFO     | __main__:process_queue:110 - Starting background processing thread
2025-05-10 10:45:52.704 | INFO     | __main__:start_processing:349 - Started background processing thread
2025-05-10 10:46:27.831 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 212 in mode registration
2025-05-10 10:46:29.692 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 212 in mode registration
2025-05-10 10:46:29.699 | INFO     | __main__:offer:900 - Received WebRTC offer for connection ea4b6263-0126-4e0b-8049-163d5cd2b568 in mode registration
2025-05-10 10:46:29.710 | INFO     | __main__:on_track:942 - Track received from client ea4b6263-0126-4e0b-8049-163d5cd2b568: video
2025-05-10 10:46:29.710 | INFO     | __main__:__init__:541 - Created video transform track for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:34.716 | INFO     | __main__:offer:973 - Created WebRTC connection for ea4b6263-0126-4e0b-8049-163d5cd2b568 in 5.02 seconds
2025-05-10 10:46:34.719 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for ea4b6263-0126-4e0b-8049-163d5cd2b568: checking
2025-05-10 10:46:34.755 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for ea4b6263-0126-4e0b-8049-163d5cd2b568: new
2025-05-10 10:46:35.542 | INFO     | __main__:process_queue:163 - Captured face 1/5 for student 212
2025-05-10 10:46:36.442 | INFO     | __main__:process_queue:163 - Captured face 2/5 for student 212
2025-05-10 10:46:37.596 | INFO     | __main__:process_queue:163 - Captured face 3/5 for student 212
2025-05-10 10:46:38.521 | INFO     | __main__:process_queue:163 - Captured face 4/5 for student 212
2025-05-10 10:46:39.717 | INFO     | __main__:process_queue:163 - Captured face 5/5 for student 212
2025-05-10 10:46:39.717 | INFO     | __main__:process_queue:170 - Immediately set registration_complete flag for student 212
2025-05-10 10:46:39.717 | ERROR    | __main__:process_queue:197 - Error processing registration completion: 'numpy.ndarray' object has no attribute 'embedding'
2025-05-10 10:46:39.722 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:39.723 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:39.723 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:39.723 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:40.327 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:40.328 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:40.556 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:40.556 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:40.556 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:40.556 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:41.440 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:41.440 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:41.672 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:41.672 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:41.672 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:41.672 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:42.538 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:42.539 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:42.765 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:42.765 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:42.765 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:42.765 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:43.758 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:43.759 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:43.989 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:43.989 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:43.989 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:43.989 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:44.911 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:44.911 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:45.139 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:45.140 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:45.140 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:45.140 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:46.051 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:46.051 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:46.272 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:46.272 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:46.272 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:46.272 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:47.203 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:47.203 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:47.438 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:47.438 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:47.438 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:47.438 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:48.334 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:48.334 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:48.579 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:48.579 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:48.579 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:48.579 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:49.474 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:49.475 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:49.706 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:49.706 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:49.706 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:49.706 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:50.604 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:50.604 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:50.833 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:50.833 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:50.833 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:50.833 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:51.751 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:51.751 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:51.982 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:51.982 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:51.982 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:51.982 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:52.864 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:52.865 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:53.097 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:53.097 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:53.098 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:53.098 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:53.098 | INFO     | __main__:process_queue:335 - Performance: 17 frames, 0 recognized (0.0%), FPS: 0.28
2025-05-10 10:46:53.988 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:53.988 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:54.227 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:54.227 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:54.227 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:54.227 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:55.043 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:55.044 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:55.292 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:55.292 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:55.292 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:55.292 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:56.260 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:56.261 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:56.501 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:56.501 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:56.501 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:56.501 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:57.328 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:57.328 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:57.567 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:57.567 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:57.567 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:57.567 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:58.397 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:58.397 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:58.633 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:58.633 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:58.633 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:58.633 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:59.464 | WARNING  | __main__:recv:801 - Data channel not available for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:59.464 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:46:59.708 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 212, retry 1/3
2025-05-10 10:46:59.708 | INFO     | __main__:process_queue:240 - Using fallback method for student 212
2025-05-10 10:46:59.708 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 212
2025-05-10 10:46:59.708 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:46:59.735 | INFO     | __main__:on_ended:953 - Track ended for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:59.735 | INFO     | __main__:recv:871 - Media stream ended for connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:59.745 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 212 in mode registration
2025-05-10 10:46:59.746 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for ea4b6263-0126-4e0b-8049-163d5cd2b568: closed
2025-05-10 10:46:59.746 | INFO     | __main__:release_webrtc:1029 - Closed connection ea4b6263-0126-4e0b-8049-163d5cd2b568
2025-05-10 10:46:59.751 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 212 in mode registration
2025-05-10 10:47:07.376 | INFO     | __main__:release_webrtc:1019 - Received release request for connection unknown in mode attendance
2025-05-10 10:47:08.199 | INFO     | __main__:release_webrtc:1019 - Received release request for connection unknown in mode attendance
2025-05-10 10:47:08.203 | INFO     | __main__:offer:900 - Received WebRTC offer for connection 9435ca34-bc31-4138-8226-2d15cb320479 in mode attendance
2025-05-10 10:47:08.206 | INFO     | __main__:on_track:942 - Track received from client 9435ca34-bc31-4138-8226-2d15cb320479: video
2025-05-10 10:47:08.206 | INFO     | __main__:__init__:541 - Created video transform track for connection 9435ca34-bc31-4138-8226-2d15cb320479
2025-05-10 10:47:13.211 | INFO     | __main__:offer:973 - Created WebRTC connection for 9435ca34-bc31-4138-8226-2d15cb320479 in 5.01 seconds
2025-05-10 10:47:13.214 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 9435ca34-bc31-4138-8226-2d15cb320479: checking
2025-05-10 10:47:13.237 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 9435ca34-bc31-4138-8226-2d15cb320479: new
2025-05-10 10:47:20.106 | ERROR    | __main__:process_queue:324 - Error marking attendance via API: cannot access local variable 'confidence' where it is not associated with a value
2025-05-10 10:47:21.168 | INFO     | __main__:on_ended:953 - Track ended for connection 9435ca34-bc31-4138-8226-2d15cb320479
2025-05-10 10:47:21.172 | INFO     | __main__:release_webrtc:1019 - Received release request for connection unknown in mode attendance
2025-05-10 10:47:21.172 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for 9435ca34-bc31-4138-8226-2d15cb320479: closed
2025-05-10 10:47:21.174 | INFO     | __main__:release_webrtc:1029 - Closed connection 9435ca34-bc31-4138-8226-2d15cb320479
2025-05-10 10:49:41.067 | INFO     | __main__:release_webrtc:1019 - Received release request for connection unknown in mode attendance
