2025-05-10 10:06:38.560 | INFO     | __main__:process_queue:110 - Starting background processing thread
2025-05-10 10:06:38.560 | INFO     | __main__:start_processing:265 - Started background processing thread
2025-05-10 10:07:22.096 | INFO     | __main__:release_webrtc:891 - Received release request for connection unknown in mode attendance
2025-05-10 10:07:22.919 | INFO     | __main__:release_webrtc:891 - Received release request for connection unknown in mode attendance
2025-05-10 10:07:22.922 | INFO     | __main__:offer:779 - Received WebRTC offer for connection 5e23a143-3ebd-4487-98e7-031d43c27fda in mode attendance
2025-05-10 10:07:22.932 | INFO     | __main__:on_track:814 - Track received from client 5e23a143-3ebd-4487-98e7-031d43c27fda: video
2025-05-10 10:07:22.932 | INFO     | __main__:__init__:457 - Created video transform track for connection 5e23a143-3ebd-4487-98e7-031d43c27fda
2025-05-10 10:07:27.938 | INFO     | __main__:offer:845 - Created WebRTC connection for 5e23a143-3ebd-4487-98e7-031d43c27fda in 5.02 seconds
2025-05-10 10:07:27.941 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for 5e23a143-3ebd-4487-98e7-031d43c27fda: checking
2025-05-10 10:07:27.975 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for 5e23a143-3ebd-4487-98e7-031d43c27fda: new
2025-05-10 10:07:38.259 | ERROR    | __main__:process_queue:240 - Error marking attendance via API: cannot access local variable 'student_id' where it is not associated with a value
2025-05-10 10:07:39.901 | INFO     | __main__:process_queue:251 - Performance: 11 frames, 0 recognized (0.0%), FPS: 0.18
2025-05-10 10:07:49.554 | ERROR    | __main__:process_queue:240 - Error marking attendance via API: cannot access local variable 'student_id' where it is not associated with a value
2025-05-10 10:08:00.748 | ERROR    | __main__:process_queue:240 - Error marking attendance via API: cannot access local variable 'student_id' where it is not associated with a value
2025-05-10 10:08:08.185 | INFO     | __main__:on_ended:825 - Track ended for connection 5e23a143-3ebd-4487-98e7-031d43c27fda
2025-05-10 10:08:08.186 | INFO     | __main__:recv:750 - Media stream ended for connection 5e23a143-3ebd-4487-98e7-031d43c27fda
2025-05-10 10:08:08.190 | INFO     | __main__:release_webrtc:891 - Received release request for connection unknown in mode attendance
2025-05-10 10:08:08.191 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for 5e23a143-3ebd-4487-98e7-031d43c27fda: closed
2025-05-10 10:08:08.192 | INFO     | __main__:release_webrtc:901 - Closed connection 5e23a143-3ebd-4487-98e7-031d43c27fda
2025-05-10 10:08:19.003 | INFO     | __main__:release_webrtc:891 - Received release request for connection 204 in mode registration
2025-05-10 10:08:20.890 | INFO     | __main__:release_webrtc:891 - Received release request for connection 204 in mode registration
2025-05-10 10:08:20.895 | INFO     | __main__:offer:779 - Received WebRTC offer for connection 2d6a5374-43a4-4510-9bab-b8fc6a9998e8 in mode registration
2025-05-10 10:08:20.897 | INFO     | __main__:on_track:814 - Track received from client 2d6a5374-43a4-4510-9bab-b8fc6a9998e8: video
2025-05-10 10:08:20.898 | INFO     | __main__:__init__:457 - Created video transform track for connection 2d6a5374-43a4-4510-9bab-b8fc6a9998e8
2025-05-10 10:08:25.903 | INFO     | __main__:offer:845 - Created WebRTC connection for 2d6a5374-43a4-4510-9bab-b8fc6a9998e8 in 5.01 seconds
2025-05-10 10:08:25.906 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for 2d6a5374-43a4-4510-9bab-b8fc6a9998e8: checking
2025-05-10 10:08:25.930 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for 2d6a5374-43a4-4510-9bab-b8fc6a9998e8: new
2025-05-10 10:08:26.496 | INFO     | __main__:process_queue:163 - Captured face 1/5 for student 204
2025-05-10 10:08:27.450 | INFO     | __main__:process_queue:163 - Captured face 2/5 for student 204
2025-05-10 10:08:28.578 | INFO     | __main__:process_queue:163 - Captured face 3/5 for student 204
2025-05-10 10:08:29.655 | INFO     | __main__:process_queue:163 - Captured face 4/5 for student 204
2025-05-10 10:08:30.597 | INFO     | __main__:process_queue:163 - Captured face 5/5 for student 204
2025-05-10 10:08:30.600 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:31.718 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:32.844 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:33.954 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:35.184 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:36.338 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:37.344 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:38.421 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:39.474 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:40.555 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:40.555 | INFO     | __main__:process_queue:251 - Performance: 51 frames, 0 recognized (0.0%), FPS: 0.42
2025-05-10 10:08:41.613 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:42.685 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:43.913 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:44.895 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:45.978 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:47.047 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:48.142 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:49.205 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:50.242 | ERROR    | __main__:process_queue:201 - Failed to register face: 403
2025-05-10 10:08:51.067 | INFO     | __main__:release_webrtc:891 - Received release request for connection 204 in mode registration
2025-05-10 10:08:51.069 | INFO     | __main__:on_ended:825 - Track ended for connection 2d6a5374-43a4-4510-9bab-b8fc6a9998e8
2025-05-10 10:08:51.071 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for 2d6a5374-43a4-4510-9bab-b8fc6a9998e8: closed
2025-05-10 10:08:51.071 | INFO     | __main__:release_webrtc:901 - Closed connection 2d6a5374-43a4-4510-9bab-b8fc6a9998e8
2025-05-10 10:10:15.276 | INFO     | __main__:stop_processing:274 - Stopping background processing thread...
2025-05-10 10:10:15.494 | INFO     | __main__:stop_processing:283 - Background processing thread stopped successfully
2025-05-10 10:10:15.494 | INFO     | __main__:on_shutdown:868 - Shutdown complete
