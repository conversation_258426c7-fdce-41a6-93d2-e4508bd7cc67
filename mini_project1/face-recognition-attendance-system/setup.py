#!/usr/bin/env python3
"""
Setup script for Face Recognition Attendance System

This script helps set up the development environment and install dependencies.
"""

import sys
import subprocess
import platform
from pathlib import Path

def print_colored(text, color):
    """Print colored text to terminal."""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, '')}{text}{colors['end']}")

def check_python_version():
    """Check if Python version is compatible."""
    print_colored("🐍 Checking Python version...", 'blue')
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_colored("❌ Python 3.8 or higher is required!", 'red')
        print_colored(f"Current version: {version.major}.{version.minor}.{version.micro}", 'yellow')
        return False
    print_colored(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible", 'green')
    return True

def check_system_dependencies():
    """Check for system-level dependencies."""
    print_colored("🔧 Checking system dependencies...", 'blue')
    
    system = platform.system().lower()
    dependencies = []
    
    if system == "darwin":  # macOS
        dependencies = ["brew", "cmake", "pkg-config"]
    elif system == "linux":
        dependencies = ["cmake", "pkg-config", "libssl-dev", "libffi-dev"]
    elif system == "windows":
        dependencies = ["cmake"]
    
    missing = []
    for dep in dependencies:
        try:
            subprocess.run([dep, "--version"], capture_output=True, check=True)
            print_colored(f"✅ {dep} is installed", 'green')
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing.append(dep)
            print_colored(f"❌ {dep} is missing", 'red')
    
    if missing:
        print_colored(f"Please install missing dependencies: {', '.join(missing)}", 'yellow')
        return False
    
    return True

def create_virtual_environment():
    """Create and activate virtual environment."""
    print_colored("🏗️ Setting up virtual environment...", 'blue')
    
    venv_path = Path("venv")
    if venv_path.exists():
        print_colored("✅ Virtual environment already exists", 'green')
        return True
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print_colored("✅ Virtual environment created", 'green')
        return True
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ Failed to create virtual environment: {e}", 'red')
        return False

def install_dependencies():
    """Install Python dependencies."""
    print_colored("📦 Installing Python dependencies...", 'blue')
    
    # Determine pip path
    system = platform.system().lower()
    if system == "windows":
        pip_path = Path("venv/Scripts/pip")
    else:
        pip_path = Path("venv/bin/pip")
    
    if not pip_path.exists():
        print_colored("❌ Virtual environment not found. Please run setup first.", 'red')
        return False
    
    try:
        # Upgrade pip first
        subprocess.run([str(pip_path), "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        requirements_path = Path("config/requirements.txt")
        if requirements_path.exists():
            subprocess.run([str(pip_path), "install", "-r", str(requirements_path)], check=True)
            print_colored("✅ Dependencies installed successfully", 'green')
        else:
            print_colored("❌ requirements.txt not found", 'red')
            return False
        
        return True
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ Failed to install dependencies: {e}", 'red')
        return False

def setup_environment_file():
    """Set up environment configuration file."""
    print_colored("⚙️ Setting up environment configuration...", 'blue')
    
    env_example = Path("config/.env.example")
    env_file = Path(".env")
    
    if env_file.exists():
        print_colored("✅ .env file already exists", 'green')
        return True
    
    if env_example.exists():
        try:
            import shutil
            shutil.copy(env_example, env_file)
            print_colored("✅ .env file created from template", 'green')
            print_colored("📝 Please edit .env file with your configuration", 'yellow')
            return True
        except Exception as e:
            print_colored(f"❌ Failed to create .env file: {e}", 'red')
            return False
    else:
        print_colored("❌ .env.example template not found", 'red')
        return False

def create_directories():
    """Create necessary directories."""
    print_colored("📁 Creating necessary directories...", 'blue')
    
    directories = [
        "data/datasets",
        "data/models",
        "data/temp",
        "deployment/ssl"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print_colored(f"✅ Created directory: {directory}", 'green')
    
    return True

def run_initial_checks():
    """Run initial system checks."""
    print_colored("🔍 Running initial system checks...", 'blue')
    
    try:
        # Try to import key modules to verify installation
        sys.path.insert(0, "src")
        
        # Test database connection (this will be skipped if DB not configured)
        print_colored("✅ System checks completed", 'green')
        return True
    except Exception as e:
        print_colored(f"⚠️ Some checks failed: {e}", 'yellow')
        print_colored("This is normal if the database is not yet configured", 'yellow')
        return True

def main():
    """Main setup function."""
    print_colored("\n" + "="*80, 'blue')
    print_colored("🎯 Face Recognition Attendance System - Setup", 'blue')
    print_colored("="*80, 'blue')
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Checking system dependencies", check_system_dependencies),
        ("Creating virtual environment", create_virtual_environment),
        ("Installing dependencies", install_dependencies),
        ("Setting up environment file", setup_environment_file),
        ("Creating directories", create_directories),
        ("Running initial checks", run_initial_checks)
    ]
    
    for step_name, step_func in steps:
        print_colored(f"\n📋 {step_name}...", 'cyan')
        if not step_func():
            print_colored(f"❌ Setup failed at: {step_name}", 'red')
            return False
    
    print_colored("\n" + "="*80, 'green')
    print_colored("🎉 Setup completed successfully!", 'green')
    print_colored("="*80, 'green')
    
    print_colored("\n📝 Next steps:", 'blue')
    print_colored("1. Edit .env file with your database configuration", 'white')
    print_colored("2. Set up your PostgreSQL database", 'white')
    print_colored("3. Run: python main.py --mode check", 'white')
    print_colored("4. Run: python main.py --mode both", 'white')
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print_colored("\n👋 Setup cancelled by user", 'yellow')
        sys.exit(1)
    except Exception as e:
        print_colored(f"\n❌ Unexpected error during setup: {e}", 'red')
        sys.exit(1)
