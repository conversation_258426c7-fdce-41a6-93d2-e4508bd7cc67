#!/usr/bin/env python3
"""
Face Recognition Attendance System - Main Entry Point

This is the main entry point for the Face Recognition Attendance System.
It provides a unified interface to start the system components.

Usage:
    python main.py [--mode MODE] [--port PORT]

Modes:
    - app: Start the Flask web application (default)
    - webrtc: Start the WebRTC server
    - both: Start both services
    - check: Run system checks

Examples:
    python main.py                    # Start Flask app on default port
    python main.py --mode both        # Start both Flask and WebRTC servers
    python main.py --mode webrtc --port 8084  # Start WebRTC server on port 8084
    python main.py --mode check       # Run system health checks
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def start_flask_app(port=5001):
    """Start the Flask application."""
    print(f"🚀 Starting Flask application on port {port}...")
    try:
        from src.app.app import app
        app.run(host='0.0.0.0', port=port, debug=False)
    except ImportError as e:
        print(f"❌ Error importing Flask app: {e}")
        print("Make sure all dependencies are installed: pip install -r config/requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        return False

def start_webrtc_server(port=8084):
    """Start the WebRTC server."""
    print(f"🎥 Starting WebRTC server on port {port}...")
    try:
        # Use subprocess to start the WebRTC server
        cmd = [sys.executable, "src/services/webrtc_server.py", "--port", str(port)]
        subprocess.run(cmd, cwd=Path(__file__).parent)
    except Exception as e:
        print(f"❌ Error starting WebRTC server: {e}")
        return False

def run_system_checks():
    """Run system health checks."""
    print("🔍 Running system health checks...")
    try:
        from src.utils.check_model import main as check_main
        check_main()
    except ImportError as e:
        print(f"❌ Error importing check module: {e}")
        return False
    except Exception as e:
        print(f"❌ Error running checks: {e}")
        return False

def start_both_services():
    """Start both Flask and WebRTC servers using the startup script."""
    print("🚀 Starting both Flask and WebRTC servers...")
    try:
        script_path = Path(__file__).parent / "scripts" / "start_servers.py"
        subprocess.run([sys.executable, str(script_path)])
    except Exception as e:
        print(f"❌ Error starting services: {e}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Face Recognition Attendance System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--mode", 
        choices=["app", "webrtc", "both", "check"],
        default="app",
        help="Mode to run the system in (default: app)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        help="Port to run the service on (default: 5001 for app, 8084 for webrtc)"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print("\n" + "="*80)
    print("🎯 Face Recognition Attendance System")
    print("="*80)
    
    # Set default ports
    if args.mode == "app":
        port = args.port or 5001
        return start_flask_app(port)
    elif args.mode == "webrtc":
        port = args.port or 8084
        return start_webrtc_server(port)
    elif args.mode == "both":
        return start_both_services()
    elif args.mode == "check":
        return run_system_checks()
    else:
        print(f"❌ Unknown mode: {args.mode}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
