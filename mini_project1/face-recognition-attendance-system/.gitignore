# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/*.log

# Database
*.db
*.sqlite3

# Environment variables
.env
.env.local
.env.production

# Temporary files
data/temp/*
*.tmp

# Face recognition models (downloaded automatically)
models/
.insightface/

# SSL certificates (generated)
deployment/ssl/*.pem
deployment/ssl/*.key
deployment/ssl/*.crt

# Docker
.docker/

# Backup files
*.bak
*.backup

# Media files (if large)
data/datasets/*/
!data/datasets/.gitkeep

# Test coverage
.coverage
htmlcov/

# Jupyter Notebooks
.ipynb_checkpoints

# PyCharm
.idea/

# VS Code
.vscode/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
