# Face Recognition Attendance System - Environment Configuration
# Copy this file to .env and update the values for your environment

# Database Configuration
DB_USER=postgres
DB_PASS=your_password_here
DB_NAME=face_recognition_db
DB_HOST=localhost
DB_PORT=5432

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here-change-this-in-production

# WebRTC Configuration
WEBRTC_PORT=8084
WEBRTC_HOST=0.0.0.0

# Face Recognition Configuration
FACE_RECOGNITION_THRESHOLD=0.6
INSIGHTFACE_MODEL=buffalo_l

# Security Configuration
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
RATE_LIMIT_PER_MINUTE=60

# Email Configuration (for notifications)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_ROTATION=10MB
LOG_RETENTION=1week

# System Configuration
CAMERA_DEVICE=0
VIDEO_RESOLUTION=640x480
FPS_LIMIT=30

# Production Settings (set to True in production)
PRODUCTION=False
SSL_ENABLED=False
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# Backup Configuration
BACKUP_ENABLED=False
BACKUP_INTERVAL=24h
BACKUP_RETENTION=30d
