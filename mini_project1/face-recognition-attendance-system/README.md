# Face Recognition Attendance System

A comprehensive face recognition-based attendance management system built with Flask, WebRTC, and InsightFace.

## 🏗️ Project Structure

```
face-recognition-attendance-system/
├── src/                          # Source code
│   ├── core/                     # Core application files
│   │   └── app.py               # Main Flask application
│   ├── models/                   # Database models
│   │   └── db.py                # Database schema and models
│   ├── services/                 # Business logic services
│   │   ├── face_utils.py        # Face recognition service (InsightFace)
│   │   ├── webrtc_server.py     # WebRTC server
│   │   └── video_service.py     # Video processing service
│   ├── api/                      # API endpoints
│   ├── utils/                    # Utility functions
│   │   └── check_model.py       # Model verification utility
│   └── web/                      # Web interface
│       ├── static/              # CSS, JS, images
│       └── templates/           # HTML templates
├── config/                       # Configuration files
│   ├── requirements.txt         # Python dependencies
│   └── supervisord.conf         # Process management config
├── data/                         # Data storage
│   ├── datasets/                # Face image datasets
│   ├── training/                # Training data
│   └── temp/                    # Temporary files
├── deployment/                   # Deployment configurations
│   ├── docker/                  # Docker setup
│   ├── nginx/                   # Nginx configuration
│   └── ssl/                     # SSL certificates
├── scripts/                      # Utility scripts
│   └── start_servers.py         # Server startup script
├── tests/                        # Test files
├── docs/                         # Documentation
├── logs/                         # Application logs
└── venv/                         # Python virtual environment
```

## 🚀 Quick Start

1. **Activate Virtual Environment**
   ```bash
   source venv/bin/activate
   ```

2. **Install Dependencies**
   ```bash
   pip install -r config/requirements.txt
   ```

3. **Start the System**
   ```bash
   python scripts/start_servers.py
   ```

## 🔧 Configuration

- **Database**: PostgreSQL with SQLAlchemy ORM
- **Face Recognition**: InsightFace with ONNX Runtime
- **WebRTC**: Real-time video streaming
- **Web Framework**: Flask with Jinja2 templates

## 📊 Features

- Real-time face recognition attendance
- WebRTC-based video capture
- Admin dashboard and user management
- Attendance reports and analytics
- Docker deployment support
- SSL/TLS security

## 🔒 Security

- Secure face data storage
- SSL/TLS encryption
- User authentication and authorization
- Admin access controls

## 📝 Logs

All application logs are centralized in the `logs/` directory:
- `db_*.log` - Database operations
- `webrtc_*.log` - WebRTC server logs
- `app_*.log` - Main application logs
- `video_service_*.log` - Video processing logs

## 🐳 Docker Deployment

Docker configuration is available in `deployment/docker/`:
- Multi-service setup with PostgreSQL, Nginx, and the application
- SSL certificate generation
- Production-ready configuration

## 🧪 Testing

Test files should be placed in the `tests/` directory.

## 📚 Documentation

Additional documentation can be found in the `docs/` directory.

## 🤝 Contributing

1. Follow the established project structure
2. Add tests for new features
3. Update documentation as needed
4. Use proper logging practices

## 📄 License

[Add your license information here]
