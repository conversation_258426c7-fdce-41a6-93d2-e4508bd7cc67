# Face Recognition Attendance System

A professional-grade face recognition attendance system built with Python, Flask, and InsightFace.

## 🎯 Features

- **Real-time Face Recognition**: Advanced face detection and recognition using InsightFace
- **Web Interface**: Modern, responsive web application
- **WebRTC Support**: Real-time video streaming and processing
- **Database Management**: PostgreSQL with comprehensive data models
- **Docker Support**: Complete containerization for easy deployment
- **Professional Architecture**: Industry-standard project structure

## 🏗️ Architecture

```
face-recognition-attendance-system/
├── src/                    # Source code
│   ├── app/               # Flask application
│   ├── models/            # Database models
│   ├── services/          # Business logic
│   ├── utils/             # Utilities
│   └── api/               # API endpoints
├── config/                # Configuration files
├── tests/                 # Test suites
├── docs/                  # Documentation
├── scripts/               # Automation scripts
├── deployment/            # Deployment configurations
├── data/                  # Data storage
└── logs/                  # Application logs
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- PostgreSQL 12+
- Docker (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd face-recognition-attendance-system
   ```

2. **Set up virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r config/requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp config/.env.example .env
   # Edit .env with your configuration
   ```

5. **Run the application**
   ```bash
   python main.py
   ```

## 🐳 Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose -f deployment/docker/docker-compose.yml up -d
```

## 📖 Documentation

- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md)
- [Development Setup](docs/development.md)
- [Configuration Reference](docs/configuration.md)

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/

# Run with coverage
python -m pytest tests/ --cov=src/
```

## 📊 Monitoring

- **Logs**: Centralized logging in `logs/` directory
- **Health Checks**: Built-in health monitoring endpoints
- **Metrics**: Application performance metrics

## 🔧 Configuration

Key configuration files:
- `config/requirements.txt` - Python dependencies
- `config/supervisord.conf` - Process management
- `.env` - Environment variables
- `deployment/docker/` - Container configurations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in `docs/`
- Review the troubleshooting guide

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Added WebRTC support
- **v1.2.0** - Enhanced security and performance

---

**Built with ❤️ using modern Python practices and industry standards**
