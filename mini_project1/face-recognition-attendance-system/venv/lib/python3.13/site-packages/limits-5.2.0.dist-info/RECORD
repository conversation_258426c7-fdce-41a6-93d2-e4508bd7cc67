limits-5.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
limits-5.2.0.dist-info/METADATA,sha256=Zi6cYEI4CXzPLk-3CWg2ct8Pc2kyS3S2pbgqprHIyuk,10900
limits-5.2.0.dist-info/RECORD,,
limits-5.2.0.dist-info/WHEEL,sha256=Nw36Djuh_5VDukK0H78QzOX-_FQEo6V37m3nkm96gtU,91
limits-5.2.0.dist-info/licenses/LICENSE.txt,sha256=T6i7kq7F5gIPfcno9FCxU5Hcwm22Bjq0uHZV3ElcjsQ,1061
limits-5.2.0.dist-info/top_level.txt,sha256=C7g5ahldPoU2s6iWTaJayUrbGmPK1d6e9t5Nn0vQ2jM,7
limits/__init__.py,sha256=gPUFrt02kHF_syLjiVRSs-S4UVGpRMcM2VMFNhF6G24,748
limits/__pycache__/__init__.cpython-313.pyc,,
limits/__pycache__/_version.cpython-313.pyc,,
limits/__pycache__/errors.cpython-313.pyc,,
limits/__pycache__/limits.cpython-313.pyc,,
limits/__pycache__/strategies.cpython-313.pyc,,
limits/__pycache__/typing.cpython-313.pyc,,
limits/__pycache__/util.cpython-313.pyc,,
limits/__pycache__/version.cpython-313.pyc,,
limits/_version.py,sha256=REC90CYDgOXApQ2lgMa_iUZRqu-qCVhOII4vU3XEyq0,497
limits/aio/__init__.py,sha256=yxvWb_ZmV245Hg2LqD365WC5IDllcGDMw6udJ1jNp1g,118
limits/aio/__pycache__/__init__.cpython-313.pyc,,
limits/aio/__pycache__/strategies.cpython-313.pyc,,
limits/aio/storage/__init__.py,sha256=vKeArUnN1ld_0mQOBBZPCjaQgM5xI1GBPM7_F2Ydz5c,646
limits/aio/storage/__pycache__/__init__.cpython-313.pyc,,
limits/aio/storage/__pycache__/base.cpython-313.pyc,,
limits/aio/storage/__pycache__/memory.cpython-313.pyc,,
limits/aio/storage/__pycache__/mongodb.cpython-313.pyc,,
limits/aio/storage/base.py,sha256=56UyNz3I3J-4pQecjsaCK4pUC4L3R_9GzDnutdTrfKs,6706
limits/aio/storage/memcached/__init__.py,sha256=SjAEgxC6hPjobtyTf7tq3vThPMMbS4lGdtTo5kvoz64,6885
limits/aio/storage/memcached/__pycache__/__init__.cpython-313.pyc,,
limits/aio/storage/memcached/__pycache__/bridge.cpython-313.pyc,,
limits/aio/storage/memcached/__pycache__/emcache.cpython-313.pyc,,
limits/aio/storage/memcached/__pycache__/memcachio.cpython-313.pyc,,
limits/aio/storage/memcached/bridge.py,sha256=3CEruS6LvZWDQPGPLlwY4hemy6oN0WWduUE7t8vyXBI,2017
limits/aio/storage/memcached/emcache.py,sha256=J01jP-Udd2fLgamCh2CX9NEIvhN8eZVTzUok096Bbe4,3833
limits/aio/storage/memcached/memcachio.py,sha256=OoGVqOVG0pVX2McFeTGQ_AbiqQUu_FYwWItpQMtNV7g,3491
limits/aio/storage/memory.py,sha256=SkW3Fgq7LjTjfBVnrVbKgjGMDqPexwCaY_C7zLHTCKU,9818
limits/aio/storage/mongodb.py,sha256=0kwDyivA53ZIOUH4DNnCjVG3olLJqAWhXctjPrnHUp0,19252
limits/aio/storage/redis/__init__.py,sha256=RjGus6rj-RhUR4eqTcnpxgicCt_rPtwFkC_SmbKfoqQ,15032
limits/aio/storage/redis/__pycache__/__init__.cpython-313.pyc,,
limits/aio/storage/redis/__pycache__/bridge.cpython-313.pyc,,
limits/aio/storage/redis/__pycache__/coredis.cpython-313.pyc,,
limits/aio/storage/redis/__pycache__/redispy.cpython-313.pyc,,
limits/aio/storage/redis/__pycache__/valkey.cpython-313.pyc,,
limits/aio/storage/redis/bridge.py,sha256=tz6WGViOqIm81hjGPUOBlz-Qw0tSB71NIttn7Xb5lok,3189
limits/aio/storage/redis/coredis.py,sha256=IzfEyXBvQbr4QUWML9xAd87a2aHCvglOBEjAg-Vq4z0,7420
limits/aio/storage/redis/redispy.py,sha256=HS1H6E9g0dP3G-8tSUILIFoc8JWpeRQOiBxcpL3I0gM,8310
limits/aio/storage/redis/valkey.py,sha256=f_-HPZhzNspywGybMNIL0F5uDZk76v8_K9wuC5ZeKhc,248
limits/aio/strategies.py,sha256=XSNE0SOSbJDjlodA_8AsL7oBYdfIn3JYh6iuR2z_cyU,10132
limits/errors.py,sha256=s1el9Vg0ly-z92guvnvYNgKi3_aVqpiw_sufemiLLTI,662
limits/limits.py,sha256=YzzZP8_ay_zlMMnnY2xhAcFTTFvFe5HEk8NQlvUTru4,4907
limits/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
limits/resources/redis/lua_scripts/acquire_moving_window.lua,sha256=Vz0HkI_bSFLW668lEVw8paKlTLEuU4jZk1fpdSuz3zg,594
limits/resources/redis/lua_scripts/acquire_sliding_window.lua,sha256=OhVI1MAN_gT92P6r-2CEmvy1yvQVjYCCZxWIxfXYceY,1329
limits/resources/redis/lua_scripts/clear_keys.lua,sha256=zU0cVfLGmapRQF9x9u0GclapM_IB2pJLszNzVQ1QRK4,184
limits/resources/redis/lua_scripts/incr_expire.lua,sha256=Uq9NcrrcDI-F87TDAJexoSJn2SDgeXIUEYozCp9S3oA,195
limits/resources/redis/lua_scripts/moving_window.lua,sha256=zlieQwfET0BC7sxpfiOuzPa1wwmrwWLy7IF8LxNa_Lw,717
limits/resources/redis/lua_scripts/sliding_window.lua,sha256=qG3Yg30Dq54QpRUcR9AOrKQ5bdJiaYpCacTm6Kxblvc,713
limits/storage/__init__.py,sha256=9iNxIlwzLQw2d54EcMa2LBJ47wiWCPOnHgn6ddqKkDI,2652
limits/storage/__pycache__/__init__.cpython-313.pyc,,
limits/storage/__pycache__/base.cpython-313.pyc,,
limits/storage/__pycache__/memcached.cpython-313.pyc,,
limits/storage/__pycache__/memory.cpython-313.pyc,,
limits/storage/__pycache__/mongodb.cpython-313.pyc,,
limits/storage/__pycache__/redis.cpython-313.pyc,,
limits/storage/__pycache__/redis_cluster.cpython-313.pyc,,
limits/storage/__pycache__/redis_sentinel.cpython-313.pyc,,
limits/storage/__pycache__/registry.cpython-313.pyc,,
limits/storage/base.py,sha256=QFVhOS8VdR7PDhaYMSc77SLg8yaGm0PCNNrMu4ZamfY,7264
limits/storage/memcached.py,sha256=AzT3vz-MnkFxS0mF3C0QjGPzCnmUt29qTnuOKhKVKYI,10455
limits/storage/memory.py,sha256=4W8hWIEzwQpoh1z0LcfwuP6DqeFoVuOEM2u8WpZkfdQ,8957
limits/storage/mongodb.py,sha256=Cg_Vj33N7Ozxdmq7RGMCerg1XuVOhRAU7eusfhiSZBc,18170
limits/storage/redis.py,sha256=zTwxV5qosxGBTrkZmD4UWQdvavDbWpYHXY7H3hXH-Sw,10791
limits/storage/redis_cluster.py,sha256=GkL8GCQFfxDriMzsPMkaj6pMEX5FvQXYpUtXLY5q8fQ,4621
limits/storage/redis_sentinel.py,sha256=OSb61DxgUxMgXSIjaM_pF5-entD8XntD56xt0rFu89k,4479
limits/storage/registry.py,sha256=CxSaDBGR5aBJPFAIsfX9axCnbcThN3Bu-EH4wHrXtu8,650
limits/strategies.py,sha256=blVIWefjRmDZvD78tneZ9aHUgCGWddyHjJaPuKxP07s,10169
limits/typing.py,sha256=pVt5D23MhQSUGqi0MBG5FCSqDwta2ygu18BpKvJFxow,3283
limits/util.py,sha256=nk5QYvezFuXPq1OTEj04RrZFSWIH-khT0e_Dim6zGCw,6002
limits/version.py,sha256=YwkF3dtq1KGzvmL3iVGctA8NNtGlK_0arrzZkZGVjUs,47
