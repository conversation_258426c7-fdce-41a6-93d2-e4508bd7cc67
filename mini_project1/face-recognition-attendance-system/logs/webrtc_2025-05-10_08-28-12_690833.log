2025-05-10 08:28:12.695 | INFO     | __main__:process_queue:80 - Starting background processing thread
2025-05-10 08:28:12.695 | INFO     | __main__:start_processing:162 - Started background processing thread
2025-05-10 08:30:28.503 | INFO     | __main__:release_webrtc:607 - Received release request for connection unknown in mode attendance
2025-05-10 08:30:29.328 | INFO     | __main__:release_webrtc:607 - Received release request for connection unknown in mode attendance
2025-05-10 08:30:29.330 | INFO     | __main__:offer:498 - Received WebRTC offer for connection 985ab167-12fa-46d7-a154-8adb43fbf6e5 in mode attendance
2025-05-10 08:30:29.342 | INFO     | __main__:on_track:518 - Track received from client 985ab167-12fa-46d7-a154-8adb43fbf6e5: video
2025-05-10 08:30:29.342 | INFO     | __main__:__init__:349 - Created video transform track for connection 985ab167-12fa-46d7-a154-8adb43fbf6e5
2025-05-10 08:30:34.344 | ERROR    | __main__:offer:552 - Timeout creating answer for 985ab167-12fa-46d7-a154-8adb43fbf6e5
2025-05-10 08:30:34.345 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for 985ab167-12fa-46d7-a154-8adb43fbf6e5: closed
2025-05-10 08:30:34.352 | INFO     | __main__:offer:498 - Received WebRTC offer for connection 798cef6b-74e1-4269-bed1-f835b3889d0d in mode attendance
2025-05-10 08:30:34.357 | INFO     | __main__:on_track:518 - Track received from client 798cef6b-74e1-4269-bed1-f835b3889d0d: video
2025-05-10 08:30:34.357 | INFO     | __main__:__init__:349 - Created video transform track for connection 798cef6b-74e1-4269-bed1-f835b3889d0d
2025-05-10 08:30:39.359 | ERROR    | __main__:offer:552 - Timeout creating answer for 798cef6b-74e1-4269-bed1-f835b3889d0d
2025-05-10 08:30:39.359 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for 798cef6b-74e1-4269-bed1-f835b3889d0d: closed
2025-05-10 08:30:40.375 | INFO     | __main__:release_webrtc:607 - Received release request for connection unknown in mode attendance
2025-05-10 08:30:40.380 | INFO     | __main__:offer:498 - Received WebRTC offer for connection f977769c-0953-4b48-b34d-90a1bc6eabbe in mode attendance
2025-05-10 08:30:40.384 | INFO     | __main__:on_track:518 - Track received from client f977769c-0953-4b48-b34d-90a1bc6eabbe: video
2025-05-10 08:30:40.385 | INFO     | __main__:__init__:349 - Created video transform track for connection f977769c-0953-4b48-b34d-90a1bc6eabbe
2025-05-10 08:30:45.387 | ERROR    | __main__:offer:552 - Timeout creating answer for f977769c-0953-4b48-b34d-90a1bc6eabbe
2025-05-10 08:30:45.388 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for f977769c-0953-4b48-b34d-90a1bc6eabbe: closed
2025-05-10 08:30:45.397 | INFO     | __main__:offer:498 - Received WebRTC offer for connection 0fca81f9-126e-468b-b214-3b3eb606d072 in mode attendance
2025-05-10 08:30:45.449 | INFO     | __main__:on_track:518 - Track received from client 0fca81f9-126e-468b-b214-3b3eb606d072: video
2025-05-10 08:30:45.449 | INFO     | __main__:__init__:349 - Created video transform track for connection 0fca81f9-126e-468b-b214-3b3eb606d072
2025-05-10 08:30:50.451 | ERROR    | __main__:offer:552 - Timeout creating answer for 0fca81f9-126e-468b-b214-3b3eb606d072
2025-05-10 08:30:50.452 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for 0fca81f9-126e-468b-b214-3b3eb606d072: closed
2025-05-10 08:30:50.464 | INFO     | __main__:release_webrtc:607 - Received release request for connection unknown in mode attendance
2025-05-10 08:30:51.610 | INFO     | __main__:release_webrtc:607 - Received release request for connection unknown in mode attendance
2025-05-10 08:30:51.612 | INFO     | __main__:offer:498 - Received WebRTC offer for connection cbde3543-c8ff-4683-a9e1-3094209d7b17 in mode attendance
2025-05-10 08:30:51.616 | INFO     | __main__:on_track:518 - Track received from client cbde3543-c8ff-4683-a9e1-3094209d7b17: video
2025-05-10 08:30:51.616 | INFO     | __main__:__init__:349 - Created video transform track for connection cbde3543-c8ff-4683-a9e1-3094209d7b17
2025-05-10 08:30:56.618 | ERROR    | __main__:offer:552 - Timeout creating answer for cbde3543-c8ff-4683-a9e1-3094209d7b17
2025-05-10 08:30:56.619 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for cbde3543-c8ff-4683-a9e1-3094209d7b17: closed
2025-05-10 08:30:56.627 | INFO     | __main__:offer:498 - Received WebRTC offer for connection 41e6efb4-9b34-4898-bfe6-31d9b41924a1 in mode attendance
2025-05-10 08:30:56.631 | INFO     | __main__:on_track:518 - Track received from client 41e6efb4-9b34-4898-bfe6-31d9b41924a1: video
2025-05-10 08:30:56.632 | INFO     | __main__:__init__:349 - Created video transform track for connection 41e6efb4-9b34-4898-bfe6-31d9b41924a1
2025-05-10 08:31:01.635 | ERROR    | __main__:offer:552 - Timeout creating answer for 41e6efb4-9b34-4898-bfe6-31d9b41924a1
2025-05-10 08:31:01.635 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for 41e6efb4-9b34-4898-bfe6-31d9b41924a1: closed
2025-05-10 08:31:45.564 | INFO     | __main__:stop_processing:171 - Stopping background processing thread...
2025-05-10 08:31:45.985 | INFO     | __main__:stop_processing:180 - Background processing thread stopped successfully
2025-05-10 08:31:45.985 | INFO     | __main__:on_shutdown:584 - Shutdown complete
