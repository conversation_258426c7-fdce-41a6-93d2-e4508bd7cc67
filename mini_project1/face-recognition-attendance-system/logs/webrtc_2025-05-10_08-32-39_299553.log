2025-05-10 08:32:39.303 | INFO     | __main__:process_queue:80 - Starting background processing thread
2025-05-10 08:32:39.303 | INFO     | __main__:start_processing:162 - Started background processing thread
2025-05-10 08:35:45.859 | INFO     | __main__:release_webrtc:591 - Received release request for connection unknown in mode attendance
2025-05-10 08:35:46.681 | INFO     | __main__:release_webrtc:591 - Received release request for connection unknown in mode attendance
2025-05-10 08:35:46.685 | INFO     | __main__:offer:498 - Received WebRTC offer for connection f170771e-5075-43cd-aef7-e846315737df in mode attendance
2025-05-10 08:35:46.697 | INFO     | __main__:on_track:518 - Track received from client f170771e-5075-43cd-aef7-e846315737df: video
2025-05-10 08:35:46.698 | INFO     | __main__:__init__:349 - Created video transform track for connection f170771e-5075-43cd-aef7-e846315737df
2025-05-10 08:35:51.701 | INFO     | __main__:offer:545 - Created WebRTC connection for f170771e-5075-43cd-aef7-e846315737df in 5.02 seconds
2025-05-10 08:35:51.702 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for f170771e-5075-43cd-aef7-e846315737df: checking
2025-05-10 08:35:51.752 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for f170771e-5075-43cd-aef7-e846315737df: completed
2025-05-10 08:35:56.802 | INFO     | __main__:process_queue:148 - Performance: 1 frames, 0 recognized (0.0%), FPS: 0.01
2025-05-10 08:36:21.835 | INFO     | __main__:on_ended:525 - Track ended for connection f170771e-5075-43cd-aef7-e846315737df
2025-05-10 08:36:21.835 | ERROR    | __main__:recv:476 - Error in VideoTransformTrack.recv: 
2025-05-10 08:36:21.839 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for f170771e-5075-43cd-aef7-e846315737df: closed
2025-05-10 08:36:21.839 | INFO     | __main__:release_webrtc:591 - Received release request for connection unknown in mode attendance
2025-05-10 08:36:21.840 | ERROR    | __main__:release_webrtc:603 - Error closing connection f170771e-5075-43cd-aef7-e846315737df: 'f170771e-5075-43cd-aef7-e846315737df'
2025-05-10 08:36:59.690 | INFO     | __main__:release_webrtc:591 - Received release request for connection 198 in mode registration
2025-05-10 08:37:01.581 | INFO     | __main__:release_webrtc:591 - Received release request for connection 198 in mode registration
2025-05-10 08:37:01.586 | INFO     | __main__:offer:498 - Received WebRTC offer for connection d353cefa-2aeb-4adb-a00b-9ac2d57775c3 in mode registration
2025-05-10 08:37:01.589 | INFO     | __main__:on_track:518 - Track received from client d353cefa-2aeb-4adb-a00b-9ac2d57775c3: video
2025-05-10 08:37:01.589 | INFO     | __main__:__init__:349 - Created video transform track for connection d353cefa-2aeb-4adb-a00b-9ac2d57775c3
2025-05-10 08:37:06.592 | INFO     | __main__:offer:545 - Created WebRTC connection for d353cefa-2aeb-4adb-a00b-9ac2d57775c3 in 5.01 seconds
2025-05-10 08:37:06.594 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for d353cefa-2aeb-4adb-a00b-9ac2d57775c3: checking
2025-05-10 08:37:06.616 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for d353cefa-2aeb-4adb-a00b-9ac2d57775c3: completed
2025-05-10 08:37:07.390 | INFO     | __main__:process_queue:148 - Performance: 22 frames, 0 recognized (0.0%), FPS: 0.08
2025-05-10 08:37:08.691 | INFO     | __main__:on_ended:525 - Track ended for connection d353cefa-2aeb-4adb-a00b-9ac2d57775c3
2025-05-10 08:37:08.691 | ERROR    | __main__:recv:476 - Error in VideoTransformTrack.recv: 
2025-05-10 08:37:08.693 | INFO     | __main__:on_iceconnectionstatechange:508 - ICE connection state for d353cefa-2aeb-4adb-a00b-9ac2d57775c3: closed
2025-05-10 08:37:08.694 | INFO     | __main__:stop_processing:171 - Stopping background processing thread...
2025-05-10 08:37:09.079 | INFO     | __main__:stop_processing:180 - Background processing thread stopped successfully
2025-05-10 08:37:09.079 | INFO     | __main__:on_shutdown:568 - Shutdown complete
