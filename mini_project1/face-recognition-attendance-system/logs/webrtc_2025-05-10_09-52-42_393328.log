2025-05-10 09:52:42.397 | INFO     | __main__:process_queue:110 - Starting background processing thread
2025-05-10 09:52:42.397 | INFO     | __main__:start_processing:265 - Started background processing thread
2025-05-10 09:53:16.062 | INFO     | __main__:release_webrtc:891 - Received release request for connection unknown in mode attendance
2025-05-10 09:53:16.850 | INFO     | __main__:release_webrtc:891 - Received release request for connection unknown in mode attendance
2025-05-10 09:53:16.851 | INFO     | __main__:offer:779 - Received WebRTC offer for connection b3fedbfa-923c-41d6-961f-766af079a131 in mode attendance
2025-05-10 09:53:16.860 | INFO     | __main__:on_track:814 - Track received from client b3fedbfa-923c-41d6-961f-766af079a131: video
2025-05-10 09:53:16.860 | INFO     | __main__:__init__:457 - Created video transform track for connection b3fedbfa-923c-41d6-961f-766af079a131
2025-05-10 09:53:21.864 | INFO     | __main__:offer:845 - Created WebRTC connection for b3fedbfa-923c-41d6-961f-766af079a131 in 5.01 seconds
2025-05-10 09:53:21.865 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for b3fedbfa-923c-41d6-961f-766af079a131: checking
2025-05-10 09:53:21.893 | INFO     | __main__:on_iceconnectionstatechange:804 - ICE connection state for b3fedbfa-923c-41d6-961f-766af079a131: new
2025-05-10 09:53:25.282 | INFO     | __main__:recv:716 - Detected 1 faces
2025-05-10 09:53:32.125 | ERROR    | __main__:process_queue:240 - Error marking attendance via API: cannot access local variable 'student_id' where it is not associated with a value
2025-05-10 09:53:33.092 | INFO     | __main__:recv:716 - Detected 1 faces
2025-05-10 09:53:43.128 | ERROR    | __main__:process_queue:240 - Error marking attendance via API: cannot access local variable 'student_id' where it is not associated with a value
2025-05-10 09:54:58.610 | INFO     | __main__:process_queue:251 - Performance: 20 frames, 0 recognized (0.0%), FPS: 0.15
