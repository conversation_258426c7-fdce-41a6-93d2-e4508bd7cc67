2025-05-10 10:37:07.660 | INFO     | __main__:process_queue:110 - Starting background processing thread
2025-05-10 10:37:07.660 | INFO     | __main__:start_processing:349 - Started background processing thread
2025-05-10 10:37:52.464 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 210 in mode registration
2025-05-10 10:37:54.347 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 210 in mode registration
2025-05-10 10:37:54.350 | INFO     | __main__:offer:900 - Received WebRTC offer for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f in mode registration
2025-05-10 10:37:54.361 | INFO     | __main__:on_track:942 - Track received from client f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f: video
2025-05-10 10:37:54.361 | INFO     | __main__:__init__:541 - Created video transform track for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:37:59.367 | INFO     | __main__:offer:973 - Created WebRTC connection for f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f in 5.02 seconds
2025-05-10 10:37:59.370 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f: checking
2025-05-10 10:37:59.408 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f: new
2025-05-10 10:38:00.075 | INFO     | __main__:process_queue:163 - Captured face 1/5 for student 210
2025-05-10 10:38:00.947 | INFO     | __main__:process_queue:163 - Captured face 2/5 for student 210
2025-05-10 10:38:02.057 | INFO     | __main__:process_queue:163 - Captured face 3/5 for student 210
2025-05-10 10:38:03.222 | INFO     | __main__:process_queue:163 - Captured face 4/5 for student 210
2025-05-10 10:38:04.297 | INFO     | __main__:process_queue:163 - Captured face 5/5 for student 210
2025-05-10 10:38:04.297 | INFO     | __main__:process_queue:170 - Immediately set registration_complete flag for student 210
2025-05-10 10:38:04.297 | ERROR    | __main__:process_queue:197 - Error processing registration completion: 'numpy.ndarray' object has no attribute 'embedding'
2025-05-10 10:38:04.306 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:04.307 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:04.307 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:04.307 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:04.982 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:04.982 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:05.382 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:05.383 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:05.383 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:05.383 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:06.052 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:06.052 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:06.415 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:06.415 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:06.415 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:06.415 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:07.116 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:07.116 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:07.346 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:07.346 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:07.346 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:07.346 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:08.214 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:08.214 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:08.453 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:08.454 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:08.454 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:08.454 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:08.454 | INFO     | __main__:process_queue:335 - Performance: 9 frames, 0 recognized (0.0%), FPS: 0.15
2025-05-10 10:38:09.354 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:09.354 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:09.584 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:09.584 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:09.584 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:09.584 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:10.488 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:10.488 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:10.711 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:10.711 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:10.711 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:10.712 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:11.521 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:11.521 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:11.755 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:11.755 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:11.755 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:11.755 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:12.658 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:12.658 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:12.888 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:12.888 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:12.888 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:12.888 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:13.765 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:13.766 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:13.994 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:13.994 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:13.994 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:13.994 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:14.990 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:14.990 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:15.229 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:15.229 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:15.229 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:15.229 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:16.136 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:16.137 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:16.367 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:16.367 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:16.367 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:16.367 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:17.267 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:17.267 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:17.493 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:17.493 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:17.493 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:17.494 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:18.373 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:18.373 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:18.612 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:18.612 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:18.612 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:18.612 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:19.509 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:19.509 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:19.731 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:19.731 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:19.731 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:19.731 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:20.650 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:20.650 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:20.877 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:20.877 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:20.877 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:20.877 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:21.784 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:21.784 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:22.012 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:22.013 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:22.013 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:22.013 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:22.921 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:22.921 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:23.154 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:23.154 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:23.154 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:23.154 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:23.988 | WARNING  | __main__:recv:801 - Data channel not available for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:23.988 | ERROR    | __main__:recv:815 - Error sending registration complete message: 
2025-05-10 10:38:24.217 | WARNING  | __main__:process_queue:234 - Authentication failed (403) for student 210, retry 1/3
2025-05-10 10:38:24.217 | INFO     | __main__:process_queue:240 - Using fallback method for student 210
2025-05-10 10:38:24.217 | WARNING  | __main__:process_queue:272 - Data channel not ready for student 210
2025-05-10 10:38:24.217 | ERROR    | __main__:process_queue:285 - Error creating task to send registration complete: no running event loop
2025-05-10 10:38:24.541 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 210 in mode registration
2025-05-10 10:38:24.543 | INFO     | __main__:on_ended:953 - Track ended for connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:24.543 | INFO     | __main__:on_iceconnectionstatechange:932 - ICE connection state for f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f: closed
2025-05-10 10:38:24.544 | INFO     | __main__:release_webrtc:1029 - Closed connection f3cd8bbc-b0d6-4b64-a06f-ec564bde6b7f
2025-05-10 10:38:24.547 | INFO     | __main__:release_webrtc:1019 - Received release request for connection 210 in mode registration
2025-05-10 10:40:47.593 | INFO     | __main__:stop_processing:358 - Stopping background processing thread...
2025-05-10 10:40:48.026 | INFO     | __main__:stop_processing:367 - Background processing thread stopped successfully
2025-05-10 10:40:48.026 | INFO     | __main__:on_shutdown:996 - Shutdown complete
